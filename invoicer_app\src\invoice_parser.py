"""
Document text extraction module for the MacInvoicer application.

This module handles text extraction from various document formats including PDFs,
Word documents, and Excel spreadsheets. For PDFs, it uses a two-tier approach:
1. Primary extraction using PyPDF2 for text-based PDFs
2. Fallback OCR extraction using Tesseract for scanned/image-based PDFs

For Word and Excel documents, it extracts structured text content that can be
processed by AI for invoice data extraction.

Key Features:
    - PDF text extraction (PyPDF2 + OCR fallback)
    - Word document text extraction (.docx, .doc)
    - Excel spreadsheet text extraction (.xlsx, .xls)
    - Support for encrypted PDFs (with empty password)
    - Configurable OCR tools (Tesseract, Poppler)
    - Comprehensive error handling and logging

Dependencies:
    - PyPDF2: For direct PDF text extraction
    - pytesseract: For OCR text extraction
    - PIL (Pillow): For image processing
    - pdf2image: For PDF to image conversion
    - poppler-utils: Required by pdf2image
    - python-docx: For Word document processing
    - openpyxl: For Excel document processing
    - pandas: For Excel data handling

Author: MacInvoicer Development Team
Version: 1.1.0
"""

import PyPDF2
import pytesseract
from PIL import Image
from pdf2image import convert_from_path
import os
import re
from config_loader import TESSERACT_CMD_PATH, POPPLER_PATH
from app_logger import app_logger
from document_converter import get_document_text

MIN_TEXT_LENGTH_FROM_PYPDF2 = 100 # If PyPDF2 extracts less than this, try OCR

if TESSERACT_CMD_PATH:
    pytesseract.tesseract_cmd = TESSERACT_CMD_PATH

def preprocess_ocr_text(text):
    """
    Preprocesses OCR extracted text to fix common OCR artifacts and improve AI parsing.
    
    This function addresses common OCR issues that can lead to incorrect data extraction:
    1. Fixes spacing issues around numbers and decimal points
    2. Identifies and properly formats total amount lines
    3. Separates incorrectly concatenated numbers
    4. Normalizes currency and total keywords
    
    Args:
        text (str): Raw OCR extracted text content.
        
    Returns:
        str: Cleaned and normalized text ready for AI processing.
        
    Note:
        Specifically addresses the issue where "23 727.64 167.36" gets misread
        as a single number 23727.64 instead of separate values.
    """
    if not text or not text.strip():
        return text
    
    app_logger.info("Applying OCR text preprocessing...")
    
    lines = text.split('\n')
    processed_lines = []
    
    for line in lines:
        original_line = line
        
        # Skip empty lines
        if not line.strip():
            processed_lines.append(line)
            continue
            
        # Check if this line likely contains total information
        is_total_line = any(keyword in line.lower() for keyword in [
            'total', 'amount', 'sum', 'due', 'balance', 'subtotal', 'net', 'gross'
        ]) or any(symbol in line for symbol in ['€', '$', '£', '¥'])
        
        # Look for patterns of numbers that might be incorrectly concatenated
        # Pattern: number(s) followed by space(s) then decimal number(s)
        # Example: "23 727.64 167.36" or "1 234.56"
        number_pattern = r'(\d{1,3})\s+(\d{2,3}\.\d{2})(\s+\d+\.\d{2})?'
        
        if is_total_line and re.search(number_pattern, line):
            # For total lines, add clear separation and labels
            line = re.sub(number_pattern, r'Qty: \1 | Total: \2\3', line)
            app_logger.debug(f"OCR preprocessing - Total line: '{original_line}' -> '{line}'")
        elif re.search(r'\d+\s+\d+\.\d{2}', line):
            # For other lines with similar patterns, just ensure proper spacing
            line = re.sub(r'(\d+)\s+(\d+\.\d{2})', r'\1 | \2', line)
            app_logger.debug(f"OCR preprocessing - Number separation: '{original_line}' -> '{line}'")
        
        # Standardize spacing around decimal numbers
        line = re.sub(r'(\d)\s+(\.\d{2})', r'\1\2', line)  # Fix "123 .45" -> "123.45"
        line = re.sub(r'(\d{1,3})\s+(\d{3}\.\d{2})', r'\1,\2', line)  # Fix "1 234.56" -> "1,234.56"
        
        # Ensure proper spacing around currency symbols
        line = re.sub(r'([€$£¥])(\d)', r'\1 \2', line)  # "€123.45" -> "€ 123.45"
        line = re.sub(r'(\d)([€$£¥])', r'\1 \2', line)  # "123.45€" -> "123.45 €"
        
        # Clean up excessive whitespace
        line = re.sub(r'\s+', ' ', line).strip()
        
        processed_lines.append(line)
    
    processed_text = '\n'.join(processed_lines)
    
    # Log a summary of changes made
    original_lines = len([l for l in text.split('\n') if l.strip()])
    processed_lines_count = len([l for l in processed_lines if l.strip()])
    
    if processed_text != text:
        app_logger.info(f"OCR preprocessing completed. Lines processed: {original_lines}")
        app_logger.debug("OCR preprocessing made formatting improvements to assist AI parsing")
    else:
        app_logger.debug("OCR preprocessing: No changes needed")
    
    return processed_text

def ocr_image(image_path_or_object):
    """
    Performs OCR (Optical Character Recognition) on a single image.
    
    This function extracts text from an image using Tesseract OCR. It can handle
    both file paths to images and PIL Image objects directly.
    
    Args:
        image_path_or_object (str or PIL.Image.Image): Either a file path to an image
                                                      or a PIL Image object.
                                                      
    Returns:
        str or None: Extracted text from the image, or None if OCR fails.
        
    Raises:
        TesseractNotFoundError: If Tesseract is not installed or not in PATH.
        Exception: For any other OCR processing errors.
        
    Note:
        Tesseract must be installed and either in the system PATH or specified
        via the TESSERACT_CMD_PATH environment variable.
    """
    app_logger.error(image_path_or_object)
    try:
        text = pytesseract.image_to_string(image_path_or_object)
        app_logger.error(text)
        return text
    except pytesseract.TesseractNotFoundError as e:
        app_logger.error("TesseractNotFoundError occurred during OCR processing.")
        app_logger.error(f"Original error: {e}")

        # Enhanced diagnostic information
        app_logger.error("=== TESSERACT DIAGNOSTIC INFORMATION ===")
        app_logger.error(f"TESSERACT_CMD_PATH from config: {TESSERACT_CMD_PATH}")
        app_logger.error(f"pytesseract.tesseract_cmd setting: {pytesseract.tesseract_cmd}")

        # Check if the configured path exists
        if TESSERACT_CMD_PATH:
            if os.path.exists(TESSERACT_CMD_PATH):
                app_logger.error(f"✓ Tesseract executable found at: {TESSERACT_CMD_PATH}")
                # Check if it's executable
                if os.access(TESSERACT_CMD_PATH, os.X_OK):
                    app_logger.error("✓ Tesseract executable has execute permissions")
                else:
                    app_logger.error("✗ Tesseract executable does NOT have execute permissions")
            else:
                app_logger.error(f"✗ Tesseract executable NOT found at configured path: {TESSERACT_CMD_PATH}")
        else:
            app_logger.error("✗ TESSERACT_CMD_PATH not configured in .env file")
            app_logger.error("Attempting to use system PATH...")

        # Try to get Tesseract version for additional diagnostics
        try:
            version_info = pytesseract.get_tesseract_version()
            app_logger.error(f"✓ Tesseract version detected: {version_info}")
        except Exception as version_error:
            app_logger.error(f"✗ Could not get Tesseract version: {version_error}")

        app_logger.error("=== END DIAGNOSTIC INFORMATION ===")
        app_logger.error("SOLUTIONS:")
        app_logger.error("1. Verify Tesseract is installed: https://github.com/tesseract-ocr/tesseract")
        app_logger.error("2. Check TESSERACT_CMD_PATH in .env file points to correct executable")
        app_logger.error("3. Ensure Tesseract executable has proper permissions")
        app_logger.error("4. Try adding Tesseract to system PATH")

        return None
    except Exception as e:
        app_logger.error(f"Error during OCR processing: {e}", exc_info=True)
        app_logger.error(f"Error type: {type(e).__name__}")

        # Additional context for common issues
        if "permission" in str(e).lower():
            app_logger.error("This appears to be a permission issue. Check file/directory permissions.")
        elif "path" in str(e).lower() or "file" in str(e).lower():
            app_logger.error("This appears to be a file path issue. Check if input file exists and is accessible.")
        elif "memory" in str(e).lower():
            app_logger.error("This appears to be a memory issue. The PDF might be too large or complex.")

        return None

def extract_text_from_pdf(pdf_path):
    """
    Extracts text from a PDF file using a two-tier approach.
    
    This function first attempts to extract text directly from the PDF using PyPDF2.
    If the extracted text is insufficient (below MIN_TEXT_LENGTH_FROM_PYPDF2), it
    falls back to OCR by converting PDF pages to images and processing them with Tesseract.
    
    The two-tier approach ensures optimal performance for text-based PDFs while
    providing OCR fallback for scanned documents or PDFs with embedded images.
    
    Args:
        pdf_path (str): Absolute path to the PDF file to process.
        
    Returns:
        str or None: Extracted text from the PDF, or None if both methods fail
                    to extract any text.
                    
    Raises:
        FileNotFoundError: If the PDF file doesn't exist.
        Exception: Various PDF processing errors are caught and logged.
        
    Note:
        - Supports encrypted PDFs with empty passwords
        - Requires Poppler for PDF to image conversion (OCR fallback)
        - Requires Tesseract for OCR functionality
        - Falls back gracefully if OCR tools are not available
    """
    extracted_text = ""
    # Attempt 1: PyPDF2
    try:
        with open(pdf_path, 'rb') as pdf_file_obj:
            pdf_reader = PyPDF2.PdfReader(pdf_file_obj)
            if pdf_reader.is_encrypted:
                try:
                    # Try with empty password
                    # PyPDF2 < 3.0.0: pdf_reader.decrypt('')
                    # PyPDF2 >= 3.0.0: Returns 0 if successful, 1 if password incorrect, 2 if not supported
                    # For simplicity, we just try. If it fails, it might raise an exception or reading will yield no text.
                    if hasattr(pdf_reader, 'decrypt') and callable(getattr(pdf_reader, 'decrypt')):
                         # Check return value if needed, or just let it proceed
                        pass # pdf_reader.decrypt('') # PyPDF2 might handle this internally now for some cases or error on open
                except Exception as ex:
                    app_logger.warning(f"Could not decrypt PDF {pdf_path}: {ex}. OCR will likely fail too if it's protected.")
            
            for page_num in range(len(pdf_reader.pages)):
                page_obj = pdf_reader.pages[page_num]
                extracted_text += page_obj.extract_text() or "" # Ensure it concatenates empty string if None
        
        if len(extracted_text.strip()) >= MIN_TEXT_LENGTH_FROM_PYPDF2:
            app_logger.info(f"Successfully extracted text from {pdf_path} using PyPDF2.")
            return preprocess_ocr_text(extracted_text.strip())
        else:
            app_logger.info(f"PyPDF2 extracted very little text ({len(extracted_text.strip())} chars) from {pdf_path}. Attempting OCR as fallback.")
    except Exception as e:
        app_logger.error(f"Error reading PDF {pdf_path} with PyPDF2: {e}. Attempting OCR as fallback.", exc_info=True)

    # Attempt 2: OCR with Tesseract if PyPDF2 failed or text was too short
    ocr_texts = []
    try:
        app_logger.info(f"Performing OCR on {pdf_path}...")

        # Enhanced diagnostic information for PDF to image conversion
        app_logger.debug(f"POPPLER_PATH from config: {POPPLER_PATH}")

        images = convert_from_path(pdf_path, poppler_path=POPPLER_PATH)
        if not images:
            app_logger.error("=== PDF TO IMAGE CONVERSION FAILED ===")
            app_logger.error(f"pdf2image could not convert {pdf_path} to images.")
            app_logger.error(f"POPPLER_PATH configured as: {POPPLER_PATH}")

            if POPPLER_PATH:
                if os.path.exists(POPPLER_PATH):
                    app_logger.error(f"✓ Poppler path exists: {POPPLER_PATH}")
                else:
                    app_logger.error(f"✗ Poppler path does NOT exist: {POPPLER_PATH}")
            else:
                app_logger.error("✗ POPPLER_PATH not configured - trying system PATH")

            app_logger.error("SOLUTIONS:")
            app_logger.error("1. Install Poppler: https://poppler.freedesktop.org/")
            app_logger.error("2. Set correct POPPLER_PATH in .env file")
            app_logger.error("3. Add Poppler to system PATH")
            app_logger.error("=== END PDF TO IMAGE DIAGNOSTIC ===")

            return extracted_text.strip() if extracted_text.strip() else None # Or return the minimal text from PyPDF2 if any

        app_logger.info(f"Successfully converted PDF to {len(images)} image(s) for OCR processing")
            
        for i, image in enumerate(images):
            # Optionally save images for debugging:
            # temp_image_path = f"temp_page_{i}.png"
            # image.save(temp_image_path, "PNG")
            # page_text = ocr_image(temp_image_path)
            # os.remove(temp_image_path)
            page_text = ocr_image(image) # Process image object directly
            if page_text:
                ocr_texts.append(page_text)
        
        full_ocr_text = "\n".join(ocr_texts)
        if full_ocr_text.strip():
            app_logger.info(f"Successfully extracted text from {pdf_path} using OCR.")
            return preprocess_ocr_text(full_ocr_text.strip())
        else:
            app_logger.warning(f"OCR attempt on {pdf_path} yielded no text.")
            return preprocess_ocr_text(extracted_text.strip()) if extracted_text.strip() else None
            
    except pytesseract.TesseractNotFoundError as e:
        app_logger.error("=== TESSERACT ERROR DURING PDF PROCESSING ===")
        app_logger.error(f"TesseractNotFoundError during PDF parse: {e}")
        app_logger.error("This error occurred during the PDF processing pipeline.")
        app_logger.error("OCR functionality will be disabled for this file.")
        app_logger.error("=== END TESSERACT ERROR ===")
        return preprocess_ocr_text(extracted_text.strip()) if extracted_text.strip() else None
    except FileNotFoundError as fnfe:
        app_logger.error("=== POPPLER/FILE ERROR DURING PDF PROCESSING ===")
        app_logger.error(f"FileNotFoundError during OCR prep for {pdf_path}: {fnfe}")
        app_logger.error(f"POPPLER_PATH configured as: {POPPLER_PATH}")

        if POPPLER_PATH:
            if os.path.exists(POPPLER_PATH):
                app_logger.error(f"✓ Poppler path exists: {POPPLER_PATH}")
            else:
                app_logger.error(f"✗ Poppler path does NOT exist: {POPPLER_PATH}")
        else:
            app_logger.error("✗ POPPLER_PATH not configured - trying system PATH")

        app_logger.error("This might be due to Poppler not being installed or not in PATH/POPPLER_PATH.")
        app_logger.error("SOLUTIONS:")
        app_logger.error("1. Install Poppler: https://poppler.freedesktop.org/")
        app_logger.error("2. Set correct POPPLER_PATH in .env file")
        app_logger.error("3. Add Poppler to system PATH")
        app_logger.error("=== END POPPLER/FILE ERROR ===")
        app_logger.warning("OCR functionality will be disabled for this file.")
        return preprocess_ocr_text(extracted_text.strip()) if extracted_text.strip() else None
    except Exception as e:
        app_logger.error(f"=== GENERAL OCR ERROR DURING PDF PROCESSING ===")
        app_logger.error(f"Error during OCR processing of {pdf_path}: {e}")
        app_logger.error(f"Error type: {type(e).__name__}")

        # Provide specific guidance based on error type
        if "pdf2image" in str(e).lower() or "poppler" in str(e).lower():
            app_logger.error("This appears to be a PDF to image conversion issue (Poppler-related).")
            app_logger.error("Check POPPLER_PATH configuration and Poppler installation.")
        elif "tesseract" in str(e).lower():
            app_logger.error("This appears to be a Tesseract-related issue.")
            app_logger.error("Check TESSERACT_CMD_PATH configuration and Tesseract installation.")
        elif "permission" in str(e).lower():
            app_logger.error("This appears to be a file permission issue.")
            app_logger.error("Check if the application has read access to the PDF file.")
        elif "memory" in str(e).lower():
            app_logger.error("This appears to be a memory issue.")
            app_logger.error("The PDF might be too large or complex for OCR processing.")

        app_logger.error("=== END GENERAL OCR ERROR ===")
        app_logger.error(f"Falling back to PyPDF2 text (if any): {len(extracted_text.strip()) if extracted_text else 0} characters")

        return preprocess_ocr_text(extracted_text.strip()) if extracted_text.strip() else None

    # If both methods yield nothing substantial
    return None if not extracted_text.strip() else preprocess_ocr_text(extracted_text.strip())

def extract_text_from_document(file_path):
    """
    Extract text from various document formats including PDFs, Word, and Excel files.

    This is the main entry point for document text extraction in the MacInvoicer
    application. It determines the file type and routes to the appropriate
    extraction method.

    Args:
        file_path (str): Absolute path to the document file to process.

    Returns:
        str or None: Extracted text from the document, or None if extraction fails.

    Supported formats:
        - .pdf: PDF documents (using existing PDF extraction pipeline)
        - .docx: Word documents
        - .doc: Legacy Word documents (limited support)
        - .xlsx: Excel spreadsheets
        - .xls: Legacy Excel spreadsheets

    Note:
        For PDF files, this function uses the existing two-tier extraction approach
        (PyPDF2 + OCR fallback). For Word and Excel files, it uses the document
        converter module to extract structured text content.
    """
    if not os.path.exists(file_path):
        app_logger.error(f"Document file not found: {file_path}")
        return None

    file_extension = os.path.splitext(file_path)[1].lower()
    filename = os.path.basename(file_path)

    app_logger.info(f"Extracting text from document: {filename} (type: {file_extension})")

    # Handle PDF files using existing pipeline
    if file_extension == '.pdf':
        return extract_text_from_pdf(file_path)

    # Handle Word and Excel files using document converter
    elif file_extension in ['.docx', '.doc', '.xlsx', '.xls']:
        try:
            extracted_text = get_document_text(file_path)
            if extracted_text:
                app_logger.info(f"Successfully extracted {len(extracted_text)} characters from {filename}")
                # Apply the same preprocessing as OCR text for consistency
                return preprocess_ocr_text(extracted_text)
            else:
                app_logger.warning(f"No text could be extracted from {filename}")
                return None
        except Exception as e:
            app_logger.error(f"Error extracting text from document {file_path}: {e}", exc_info=True)
            return None

    # Unsupported file format
    else:
        app_logger.error(f"Unsupported document format: {file_extension} for file {filename}")
        return None