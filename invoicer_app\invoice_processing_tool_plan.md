# Invoice Processing Tool: Development Plan & Guide

## 1. Project Goal

To develop a Python-based automated tool that monitors a specified directory for new invoice files (primarily PDFs), extracts relevant data using AI, appends this data to a monthly spreadsheet in Euros, maintains data consistency, tracks running totals, identifies duplicates, and logs processing details including AI confidence and issues. The tool should be flexible regarding AI provider choice (local or online) and operate on Windows 10.

## 2. Core Requirements

### 2.1. Directory Monitoring
-   **Functionality:** Continuously monitor a user-defined directory for new file creations.
-   **OS:** Windows 10.
-   **Trigger:** Initiate processing upon detection of a new file.

### 2.2. Invoice Reading & AI-Powered Data Extraction
-   **File Types:** Primarily PDF; consider flexibility for other common formats (e.g., images like JPG, PNG if feasible later).
-   **AI Assistance:** Utilize AI to read and interpret invoice content.
-   **Data to Extract (Example - to be refined):**
    -   Invoice Number
    -   Invoice Date
    -   Vendor Name
    -   Total Amount (and currency)
    -   VAT Amount (if applicable)
    -   Due Date
    -   Line items (optional, based on complexity)
-   **Currency Conversion:** Convert all monetary values to EUR if not already in EUR, based on the invoice date.

### 2.3. Spreadsheet Integration
-   **Format:** Monthly spreadsheets (e.g., `YYYY-MM_invoices.xlsx`).
-   **Action:** Append extracted data as new rows.
-   **Data Structure:** Consistent column headers and data types across all spreadsheets.
    -   Example Columns: `Processing_Date`, `Invoice_Filename`, `Invoice_ID`, `Vendor_Name`, `Invoice_Date`, `Due_Date`, `Total_Amount_EUR`, `VAT_Amount_EUR`, `Original_Currency`, `Original_Total_Amount`, `AI_Confidence_Score`, `Processing_Notes_Ref`
-   **Running Total:** Maintain a running total of `Total_Amount_EUR` within each monthly spreadsheet.

### 2.4. Data Consistency
-   **Dates:** Standardize date formats (e.g., `YYYY-MM-DD`).
-   **Numeric Values:** Ensure consistent formatting for currency values (e.g., two decimal places).
-   **Text:** Basic cleaning/normalization of extracted text (e.g., stripping extra whitespace).

### 2.5. AI Provider Flexibility
-   **Abstraction Layer:** Design the system so that the AI service (for OCR, data extraction, analysis) can be swapped.
-   **Supported Types:** Allow for both local models (e.g., via Ollama, a local Tesseract instance) and online APIs (e.g., OpenAI, Google Cloud Vision/Document AI).
-   **Configuration:** AI provider choice and API keys/endpoints should be configurable.

### 2.6. Invoice-Specific Comments & Logging
-   **Individual Document:** For each processed invoice, create a separate document (e.g., `.txt` or `.md` file, named corresponding to the invoice).
-   **Content:**
    -   AI's confidence level for the extraction.
    -   Any issues encountered during processing (e.g., unreadable sections, ambiguous data).
    -   Warnings or errors.
    -   A summary of extracted data.

### 2.7. Duplicate Detection
-   **Mechanism:** Implement a reliable method to identify and flag potential duplicate invoices.
-   **Criteria (to be refined):**
    -   File hash comparison.
    -   Comparison of key extracted data points (e.g., Invoice Number + Vendor Name + Total Amount).
-   **Action:** Flag duplicates and potentially skip reprocessing or log a warning. Do not append to spreadsheet if confirmed duplicate.

### 2.8. OS Compatibility
-   **Primary Target:** Windows 10. Python's cross-platform nature should be leveraged, but specific Windows considerations (like file path handling) should be tested.

## 3. Technical Design Considerations

### 3.1. Key Python Libraries
-   **Directory Monitoring:** `watchdog`
-   **PDF Processing:** `PyPDF2`, `pdfplumber`, or a library compatible with the chosen AI service.
-   **Spreadsheet Manipulation:** `pandas`, `openpyxl` (for `.xlsx`).
-   **AI SDKs:** `openai`, `google-cloud-aiplatform`, `requests` (for generic API calls), or specific libraries for local models.
-   **Configuration:** `python-dotenv` (for `.env` files), `configparser`, or `YAML`.
-   **Logging:** Python's built-in `logging` module.
-   **Currency Conversion:** A library like `forex-python` or an API for historical exchange rates.

### 3.2. AI Abstraction Layer
-   Define a common interface (e.g., a base class or a set of functions) for AI operations like `extract_invoice_data(file_path)`.
-   Implement concrete classes/functions for each supported AI provider, conforming to this interface.
-   A factory pattern could be used to instantiate the chosen AI service based on configuration.

### 3.3. Configuration Management
-   A central configuration file (e.g., `config.ini`, `config.yaml`) or environment variables for:
    -   Monitored directory path.
    -   Spreadsheet output path.
    -   AI provider choice.
    -   API keys and endpoints.
    -   Duplicate check sensitivity/thresholds.
    -   Logging levels.

### 3.4. Error Handling and Logging
-   Comprehensive error handling for file operations, API calls, data parsing, etc.
-   Detailed logging to a file for debugging and audit trails, in addition to the invoice-specific comment documents.

### 3.5. Duplicate Check Mechanism
-   Store a hash of processed files (e.g., SHA256 of file content).
-   Alternatively, maintain a database or simple CSV of key identifiers from processed invoices to check against.

### 3.6. Project Structure (Example)
```
macinvoicer/
├── src/
│   ├── main.py                 # Main application script
│   ├── file_monitor.py         # Directory monitoring logic (can be part of main.py initially)
│   ├── invoice_parser.py       # PDF/Image reading
│   ├── ai_handler/             # AI interaction module
│   │   ├── __init__.py
│   │   ├── base_ai.py          # Base AI interface
│   │   ├── local_ai.py         # Implementation for local models
│   │   └── cloud_ai.py         # Implementation for cloud APIs
│   │   └── dummy_ai.py         # Dummy implementation for MVP
│   ├── spreadsheet_manager.py  # Spreadsheet operations
│   ├── data_models.py          # Pydantic or dataclasses for invoice data
│   ├── utils.py                # Utility functions (date formatting, currency)
│   └── config_loader.py        # For loading configurations
├── config/
│   ├── config.ini              # Configuration file
│   └── .env.example            # Example environment variables
├── logs/                       # Application logs
│   └── app.log
├── processed_invoices_notes/   # Notes for each processed invoice
├── monitored_invoices/         # Directory to be monitored
├── output_spreadsheets/        # Where monthly spreadsheets are saved
├── tests/                      # Unit and integration tests
├── requirements.txt            # Python dependencies
├── .gitignore                  # Git ignore file
└── invoice_processing_tool_plan.md # This plan
```

## 4. Development Plan (Phased Approach)

**Phase 0: Setup & Initial Configuration (Partially Done)**
-   Set up the project structure.
-   Initialize `requirements.txt`.
-   Implement basic configuration loading (`.env` or `config.ini`).

**Phase 1: Directory Monitoring & Basic File Handling (Partially Done)**
-   Implement `file_monitor.py` using `watchdog` to detect new files.
-   Log detection of new files.
-   Basic file validation (e.g., check for PDF extension).

**Phase 2: PDF Reading & AI Integration (MVP Focus)**
-   Implement basic PDF text extraction (`invoice_parser.py`).
-   Create a **dummy** AI handler (`ai_handler/dummy_ai.py`) that returns fixed data.
-   Integrate this into the main flow: monitor -> parse -> dummy AI.

**Phase 3: Spreadsheet Appending (MVP Focus)**
-   Develop `spreadsheet_manager.py` to append (dummy) extracted data to a CSV/Excel.
-   Implement monthly spreadsheet creation/selection.

**Phase 4: Core AI Integration (Single Real Provider)**
-   Choose an initial AI provider.
-   Develop the AI handler for this provider.
-   Refine data extraction prompts/logic.

**Phase 5: Data Models & Consistency**
-   Implement `data_models.py` using Pydantic or dataclasses.
-   Ensure consistent date/number formatting.

**Phase 6: AI Abstraction & Multi-Provider Support**
-   Refactor `ai_handler` with a base class/interface.
-   Add support for a second AI provider.

**Phase 7: Duplicate Detection**
-   Implement `duplicate_checker.py`.

**Phase 8: Invoice-Specific Comment/Log Document Generation**
-   Log AI confidence, extracted data summary, issues.

**Phase 9: Currency Conversion**
-   Integrate currency conversion to EUR.

**Phase 10: Robust Error Handling, Logging, and Testing**
-   Comprehensive error handling and logging.
-   Unit and integration tests.

**Phase 11: Configuration Management Refinement**
-    Implement `config_loader.py` for robust configuration handling.

**Phase 12: User Interface (Optional - CLI initially)**
-   CLI improvements.
-   Potential simple GUI.

**Phase 13: Packaging & Documentation**
-   `README.md` for setup and usage.
-   Packaging (e.g., `PyInstaller`).

## 5. Rules for AI-Assisted Development (Cursor/Gemini)

1.  **Adherence to Plan:** Follow the development phases outlined above.
2.  **Modularity:** Create small, focused modules/functions. Refer to the proposed project structure.
3.  **Clarity:** Write clear, understandable, and well-commented Python code.
4.  **Configuration Driven:** Externalize configurations. Use `.env` for secrets.
5.  **Idempotency:** Aim for idempotent operations where sensible.
6.  **Error Handling:** Implement robust error handling and logging.
7.  **Iterative Refinement:** The AI data extraction process will likely require iteration.
8.  **Security:** Remind the user about securing API keys. Never log API keys.
9.  **Resource Management:** Ensure files are properly closed.
10. **User Custom Instructions:** Adhere to general "Custom Instructions".
11. **Ask for Clarification:** If unclear, ask before proceeding.

This plan provides a comprehensive roadmap. We can adjust it as we progress and learn more. 