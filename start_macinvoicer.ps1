# MacInvoicer Startup Script
# This script starts the main invoice processing service and the web UI

Write-Host "=== MacInvoicer Startup Script ===" -ForegroundColor Green
Write-Host "Starting MacInvoicer application..." -ForegroundColor Yellow

# Get the script directory (where this script is located)
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Write-Host "Script directory: $ScriptDir" -ForegroundColor Cyan

# Define paths
$VenvPath = Join-Path $ScriptDir ".venv"
$VenvActivateScript = Join-Path $VenvPath "Scripts\Activate.ps1"
$MainScriptPath = Join-Path $ScriptDir "invoicer_app\src\main.py"
$AppScriptPath = Join-Path $ScriptDir "invoicer_app\ui\app.py"
$PythonExe = Join-Path $VenvPath "Scripts\python.exe"

# Check if virtual environment exists
if (-not (Test-Path $VenvPath)) {
    Write-Host "ERROR: Virtual environment not found at: $VenvPath" -ForegroundColor Red
    Write-Host "Please create a virtual environment first by running:" -ForegroundColor Yellow
    Write-Host "python -m venv .venv" -ForegroundColor White
    Write-Host "Then install requirements:" -ForegroundColor Yellow
    Write-Host ".venv\Scripts\activate && pip install -r invoicer_app\requirements.txt" -ForegroundColor White
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if main.py exists
if (-not (Test-Path $MainScriptPath)) {
    Write-Host "ERROR: main.py not found at: $MainScriptPath" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if app.py exists
if (-not (Test-Path $AppScriptPath)) {
    Write-Host "ERROR: app.py not found at: $AppScriptPath" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Virtual environment found: $VenvPath" -ForegroundColor Green
Write-Host "Python executable: $PythonExe" -ForegroundColor Green

# Function to start main.py in a new window
function Start-MainProcess {
    Write-Host "Starting main.py (invoice processing service)..." -ForegroundColor Yellow
    
    # Create a new PowerShell window to run main.py
    $MainCommand = @"
& '$VenvActivateScript'
Write-Host 'Virtual environment activated for main.py' -ForegroundColor Green
Write-Host 'Starting invoice processing service...' -ForegroundColor Yellow
Write-Host 'Working directory:' (Get-Location) -ForegroundColor Cyan
Set-Location '$ScriptDir'
& '$PythonExe' '$MainScriptPath'
Write-Host 'Main process has stopped.' -ForegroundColor Red
Read-Host 'Press Enter to close this window'
"@

    # Start main.py in a new PowerShell window
    Start-Process powershell -ArgumentList "-NoExit", "-Command", $MainCommand
    Write-Host "Main.py started in new window" -ForegroundColor Green
}

# Function to start app.py in current window
function Start-AppProcess {
    Write-Host "Starting app.py (web UI)..." -ForegroundColor Yellow
    
    # Activate virtual environment in current session
    & $VenvActivateScript
    Write-Host "Virtual environment activated for app.py" -ForegroundColor Green
    
    # Change to script directory
    Set-Location $ScriptDir
    Write-Host "Working directory: $(Get-Location)" -ForegroundColor Cyan
    
    # Start Flask app
    Write-Host "Starting Flask web application..." -ForegroundColor Yellow
    Write-Host "Web UI will be available at: http://127.0.0.1:5000" -ForegroundColor Cyan
    Write-Host "Press Ctrl+C to stop the web server" -ForegroundColor Yellow
    Write-Host ""
    
    & $PythonExe $AppScriptPath
}

# Main execution
try {
    # Start main.py in background
    Start-MainProcess
    
    # Wait a moment for main.py to initialize
    Write-Host "Waiting 3 seconds for main.py to initialize..." -ForegroundColor Yellow
    Start-Sleep -Seconds 3
    
    # Start app.py in current window
    Start-AppProcess
}
catch {
    Write-Host "ERROR: An error occurred during startup: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "MacInvoicer has stopped." -ForegroundColor Yellow
Read-Host "Press Enter to exit"
