#!/usr/bin/env python3
"""
Database Migration Script for MacInvoicer
This script adds the vat_rate column to existing databases.
"""

import sqlite3
import os
import sys

def migrate_database():
    """Add vat_rate column to existing database tables if it doesn't exist."""
    
    # Get the UI directory and database path
    UI_DIR = os.path.dirname(os.path.abspath(__file__))
    DB_PATH = os.path.join(UI_DIR, 'data', 'invoices.db')
    
    if not os.path.exists(DB_PATH):
        print(f"Database not found at {DB_PATH}")
        print("No migration needed - database will be created with correct schema on first run.")
        return True
    
    print(f"Migrating database at {DB_PATH}")
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Check if vat_rate column exists in invoice_imports table
        cursor.execute("PRAGMA table_info(invoice_imports)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'vat_rate' not in columns:
            print("Adding vat_rate column to invoice_imports table...")
            cursor.execute("ALTER TABLE invoice_imports ADD COLUMN vat_rate REAL")
            print("✓ Added vat_rate column to invoice_imports")
        else:
            print("✓ vat_rate column already exists in invoice_imports")
        
        # Check if vat_rate column exists in approved_invoices table
        cursor.execute("PRAGMA table_info(approved_invoices)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'vat_rate' not in columns:
            print("Adding vat_rate column to approved_invoices table...")
            cursor.execute("ALTER TABLE approved_invoices ADD COLUMN vat_rate REAL")
            print("✓ Added vat_rate column to approved_invoices")
        else:
            print("✓ vat_rate column already exists in approved_invoices")
        
        # Commit changes
        conn.commit()
        conn.close()
        
        print("Database migration completed successfully!")
        return True
        
    except Exception as e:
        print(f"Error during migration: {e}")
        return False

if __name__ == "__main__":
    success = migrate_database()
    sys.exit(0 if success else 1)
