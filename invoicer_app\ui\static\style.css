body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Flashed Messages */
.flashed-messages {
    margin-bottom: 20px;
}

.alert {
    padding: 12px 16px;
    margin: 10px 0;
    border-radius: 4px;
    border: 1px solid transparent;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-error {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.stats {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
    flex: 1;
}

.invoice-table {
    width: 100%;
    background: white;
    border-collapse: collapse;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.invoice-table th,
.invoice-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.invoice-table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

.status-pending { background-color: #fff3cd; }
.status-approved { background-color: #d4edda; }
.status-rejected { background-color: #f8d7da; }

.btn {
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    display: inline-block;
    background-color: #007bff;
    color: white;
}

.btn:hover { background-color: #0056b3; }
.btn-success { background-color: #28a745; }
.btn-success:hover { background-color: #1e7e34; }
.btn-danger { background-color: #dc3545; }
.btn-danger:hover { background-color: #bd2130; }
.btn-primary { background-color: #007bff; }
.btn-sm {
    padding: 6px 12px;
    font-size: 14px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn:disabled:hover {
    background-color: inherit;
}

.review-container {
    max-width: 1600px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.review-content {
    display: flex;
    gap: 20px;
    height: 80vh;
}

.pdf-section {
    flex: 0 0 60%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pdf-controls {
    margin-bottom: 10px;
    display: flex;
    gap: 10px;
    align-items: center;
}

.pdf-controls button {
    padding: 5px 10px;
    border: 1px solid #ccc;
    background: white;
    border-radius: 4px;
    cursor: pointer;
}

#pdf-canvas {
    max-width: 100%;
    border: 1px solid #ddd;
}

.form-section {
    flex: 1;
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow-y: auto;
}

.field-group {
    margin-bottom: 15px;
}

.field-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.field-group input,
.field-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}

.action-buttons {
    margin-top: 20px;
    display: flex;
    gap: 10px;
}

.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.4);
}

.modal-content {
    background-color: white;
    margin: 15% auto;
    padding: 20px;
    border-radius: 8px;
    width: 500px;
    max-width: 90%;
}

.modal-buttons {
    margin-top: 15px;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.export-section {
    margin-top: 30px;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
} 