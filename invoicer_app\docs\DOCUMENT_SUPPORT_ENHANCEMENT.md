# Document Support Enhancement

## Overview

This enhancement adds comprehensive support for Word and Excel documents to the MacInvoicer application, expanding beyond the original PDF-only functionality. The system can now process invoices in multiple formats while maintaining the existing robust PDF processing pipeline.

## Supported File Formats

### Previously Supported
- **PDF (.pdf)**: Full support with PyPDF2 + OCR fallback

### Newly Added
- **Word Documents**:
  - `.docx`: Modern Word format (full support)
  - `.doc`: Legacy Word format (limited support, returns None currently)
- **Excel Spreadsheets**:
  - `.xlsx`: Modern Excel format (full support)
  - `.xls`: Legacy Excel format (full support with fallback handling)

## Key Features

### 1. Unified Document Processing
- Single entry point: `extract_text_from_document(file_path)`
- Automatic format detection based on file extension
- Consistent text preprocessing for all formats

### 2. Text Extraction Capabilities
- **Word Documents**: Extracts paragraphs and table content with structured formatting
- **Excel Spreadsheets**: Processes all sheets with tabular data preservation
- **PDF Documents**: Uses existing two-tier extraction (PyPDF2 + OCR)

### 3. Hybrid Processing Support
- For PDF files: Uses existing hybrid processing pipeline
- For Word/Excel files: Converts to temporary PDF for OpenAI file upload
- Automatic fallback to text-only processing if PDF conversion fails

### 4. Robust Error Handling
- Graceful degradation when libraries are missing
- Comprehensive logging for troubleshooting
- Automatic cleanup of temporary files

## Technical Implementation

### New Modules

#### `document_converter.py`
Core module for document processing with the following functions:

- `convert_docx_to_text(file_path)`: Extract text from Word documents
- `convert_xlsx_to_text(file_path)`: Extract text from Excel spreadsheets
- `convert_xls_to_text(file_path)`: Handle legacy Excel format
- `get_document_text(file_path)`: Main entry point for document text extraction
- `convert_document_to_pdf_for_hybrid(file_path)`: Create temporary PDF for hybrid processing
- `cleanup_temp_file(file_path)`: Clean up temporary files

#### Enhanced `invoice_parser.py`
- Added `extract_text_from_document(file_path)`: Unified document processing entry point
- Maintains backward compatibility with existing `extract_text_from_pdf()`
- Applies consistent text preprocessing to all document types

### Updated Components

#### `main.py`
- Modified `process_invoice()` to handle multiple file types
- Added support for temporary PDF creation in hybrid mode
- Enhanced file type validation
- Automatic cleanup of temporary files

#### Web Interface (`app.py` and `upload.html`)
- Expanded `ALLOWED_EXTENSIONS` to include new formats
- Updated upload interface messaging
- Enhanced file type validation

## Dependencies

### New Required Libraries
```
python-docx>=0.8.11    # Word document processing
reportlab>=3.6.0       # PDF generation for hybrid processing
xlrd>=2.0.1           # Legacy Excel file support
```

### Existing Libraries (Enhanced Usage)
```
openpyxl>=3.0.0       # Now used for Excel document reading
pandas>=1.3.0         # Enhanced for Excel data handling
```

## Processing Workflow

### Text-Only Processing
1. File uploaded to monitored directory
2. `extract_text_from_document()` called based on file extension
3. For Word/Excel: Extract structured text content
4. For PDF: Use existing PyPDF2 + OCR pipeline
5. Send extracted text to OpenAI for data extraction

### Hybrid Processing
1. File uploaded to monitored directory
2. For PDF files: Use existing hybrid processing
3. For Word/Excel files:
   - Extract text content
   - Create temporary PDF using reportlab
   - Upload temporary PDF to OpenAI
   - Process using hybrid pipeline
   - Clean up temporary PDF
4. Fallback to text-only if PDF conversion fails

## Error Handling

### Missing Dependencies
- Graceful degradation when optional libraries are unavailable
- Clear error messages and logging
- Fallback processing where possible

### File Processing Errors
- Comprehensive exception handling for each file type
- Detailed error logging for troubleshooting
- Continuation of processing for other files

### Temporary File Management
- Automatic cleanup of temporary PDF files
- Error handling for cleanup failures
- Prevention of disk space issues

## Testing

### Unit Tests
- `test_document_converter.py`: Tests for document conversion functionality
- Enhanced `test_invoice_parser.py`: Tests for unified document processing
- Mock-based testing for library dependencies

### Test Coverage
- Document text extraction for all supported formats
- PDF conversion for hybrid processing
- Error handling scenarios
- Temporary file cleanup

## Usage Examples

### Processing a Word Invoice
```python
from invoice_parser import extract_text_from_document

# Extract text from Word document
text = extract_text_from_document('invoice.docx')
if text:
    # Process with AI
    extracted_data = extract_invoice_data_openai(text, 'invoice.docx')
```

### Processing an Excel Invoice
```python
# Extract text from Excel spreadsheet
text = extract_text_from_document('invoice.xlsx')
if text:
    # Text includes all sheets with structured formatting
    extracted_data = extract_invoice_data_openai(text, 'invoice.xlsx')
```

### Hybrid Processing with Document Conversion
```python
from document_converter import convert_document_to_pdf_for_hybrid, cleanup_temp_file

# Convert document to PDF for hybrid processing
temp_pdf = convert_document_to_pdf_for_hybrid('invoice.docx')
if temp_pdf:
    try:
        # Use hybrid processing with temporary PDF
        extracted_data = extract_invoice_data_hybrid(temp_pdf, 'invoice.docx')
    finally:
        # Always clean up temporary file
        cleanup_temp_file(temp_pdf)
```

## Configuration

No additional configuration is required. The system automatically:
- Detects available libraries and adjusts functionality
- Handles missing dependencies gracefully
- Uses existing configuration for AI processing and file paths

## Backward Compatibility

- All existing PDF processing functionality remains unchanged
- Existing API calls continue to work without modification
- No breaking changes to configuration or usage patterns

## Future Enhancements

### Potential Improvements
1. **Enhanced .doc Support**: Add python-docx2txt or LibreOffice integration
2. **PowerPoint Support**: Add .pptx/.ppt processing capabilities
3. **Image Processing**: Direct image file support (.jpg, .png)
4. **Advanced Excel Features**: Chart and formula extraction
5. **Batch Processing**: Optimized handling of multiple documents

### Performance Optimizations
1. **Caching**: Cache converted PDFs for repeated processing
2. **Parallel Processing**: Multi-threaded document conversion
3. **Memory Management**: Streaming processing for large documents

## Troubleshooting

### Common Issues
1. **Missing Libraries**: Install required dependencies via pip
2. **File Corruption**: Check file integrity and format
3. **Memory Issues**: Process large files in smaller chunks
4. **Permission Errors**: Ensure proper file system permissions

### Logging
All document processing activities are logged with appropriate levels:
- INFO: Successful processing steps
- WARNING: Fallback processing or minor issues
- ERROR: Processing failures with detailed context
- DEBUG: Detailed processing information for troubleshooting

## Conclusion

This enhancement significantly expands the MacInvoicer's document processing capabilities while maintaining the robust, reliable architecture of the original system. Users can now process invoices in multiple formats with confidence, knowing that the system will handle various document types gracefully and efficiently.
