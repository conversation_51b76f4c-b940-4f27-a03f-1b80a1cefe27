# MacInvoicer Configuration Guide

This guide covers all configuration options available in MacInvoicer, from basic setup to advanced customization.

## Table of Contents

1. [Configuration Overview](#configuration-overview)
2. [Environment Variables](#environment-variables)
3. [OpenAI Configuration](#openai-configuration)
4. [System Tool Configuration](#system-tool-configuration)
5. [Directory Configuration](#directory-configuration)
6. [Model Selection Guide](#model-selection-guide)
7. [Advanced Settings](#advanced-settings)
8. [Configuration Validation](#configuration-validation)
9. [Environment-Specific Configurations](#environment-specific-configurations)

## Configuration Overview

MacInvoicer uses environment variables loaded from a `.env` file for configuration. This approach provides:
- **Security**: API keys and sensitive data are not hardcoded
- **Flexibility**: Easy switching between development and production settings
- **Portability**: Configuration travels with the application

### Configuration File Location
```
macinvoicer/
└── config/
    ├── .env              # Your configuration (not in version control)
    └── .env.example      # Example configuration template
```

## Environment Variables

### Required Variables

#### OPENAI_API_KEY
**Description**: Your OpenAI API key for accessing AI services.
**Required**: Yes
**Format**: String (API key)
**Example**: `OPENAI_API_KEY=sk-proj-abc123...`

**Obtaining an API Key**:
1. Visit [OpenAI Platform](https://platform.openai.com/)
2. Sign in or create an account
3. Navigate to API Keys
4. Create a new API key
5. Copy the key (you won't see it again)

### Optional Variables

#### OPENAI_MODEL
**Description**: The OpenAI model to use for invoice processing.
**Required**: No
**Default**: `gpt-4.1-nano`
**Format**: String (model name)
**Example**: `OPENAI_MODEL=gpt-4o-mini`

#### TESSERACT_CMD_PATH
**Description**: Full path to the Tesseract executable.
**Required**: No (if Tesseract is in system PATH)
**Format**: String (file path)
**Examples**:
- Windows: `C:\Program Files\Tesseract-OCR\tesseract.exe`
- Linux: `/usr/bin/tesseract`
- macOS: `/opt/homebrew/bin/tesseract`

#### POPPLER_PATH
**Description**: Path to Poppler utilities directory.
**Required**: No (if Poppler is in system PATH)
**Format**: String (directory path)
**Examples**:
- Windows: `C:\Program Files\poppler\bin`
- Linux: `/usr/bin`
- macOS: `/opt/homebrew/bin`

## OpenAI Configuration

### Model Selection

MacInvoicer supports multiple OpenAI models optimized for different use cases:

#### Cost-Effective Models (Recommended)
```env
# Most cost-effective for simple invoices
OPENAI_MODEL=gpt-4.1-nano

# Good balance of cost and performance
OPENAI_MODEL=gpt-4o-mini

# Slightly more capable, still cost-effective
OPENAI_MODEL=gpt-4.1-mini
```

#### Advanced Models
```env
# For complex invoices requiring detailed analysis
OPENAI_MODEL=gpt-4o

# For invoices requiring reasoning capabilities
OPENAI_MODEL=o1-mini

# Latest reasoning model
OPENAI_MODEL=o3-mini
```

#### Specific Model Versions
```env
# Use specific model versions for consistency
OPENAI_MODEL=gpt-4o-2024-11-20
OPENAI_MODEL=gpt-4.1-nano-2025-04-14
OPENAI_MODEL=o3-mini-2025-01-31
```

### Cost Optimization

**Model Cost Comparison** (per 1K tokens):
| Model | Input Cost | Output Cost | Best For |
|-------|------------|-------------|----------|
| gpt-4.1-nano | $0.00005 | $0.0002 | Simple invoices |
| gpt-4o-mini | $0.00015 | $0.0006 | Standard invoices |
| gpt-4.1-mini | $0.0001 | $0.0004 | Balanced processing |
| gpt-4o | $0.005 | $0.015 | Complex invoices |
| o1-mini | $0.003 | $0.012 | Reasoning required |

**Cost Optimization Tips**:
1. Start with `gpt-4.1-nano` for testing
2. Monitor costs in logs
3. Use `gpt-4o-mini` for production balance
4. Reserve advanced models for complex cases

## System Tool Configuration

### Tesseract OCR Configuration

#### Automatic Detection
If Tesseract is installed via package manager or in system PATH:
```env
# No configuration needed
# TESSERACT_CMD_PATH=  # Leave blank or omit
```

#### Manual Path Configuration
If Tesseract is installed in a custom location:

**Windows**:
```env
TESSERACT_CMD_PATH=C:\Program Files\Tesseract-OCR\tesseract.exe
```

**Linux/macOS**:
```env
TESSERACT_CMD_PATH=/usr/local/bin/tesseract
```

#### Verification
Test Tesseract configuration:
```bash
# Test direct command
tesseract --version

# Test configured path
"C:\Program Files\Tesseract-OCR\tesseract.exe" --version  # Windows
/usr/local/bin/tesseract --version  # Linux/macOS
```

### Poppler Configuration

#### Automatic Detection
If Poppler is in system PATH:
```env
# No configuration needed
# POPPLER_PATH=  # Leave blank or omit
```

#### Manual Path Configuration
**Windows**:
```env
POPPLER_PATH=C:\Program Files\poppler\bin
```

**Linux/macOS**:
```env
POPPLER_PATH=/usr/local/bin
```

#### Verification
Test Poppler configuration:
```bash
# Test PDF to image conversion
pdftoppm -h

# Test configured path
"C:\Program Files\poppler\bin\pdftoppm.exe" -h  # Windows
/usr/local/bin/pdftoppm -h  # Linux/macOS
```

## Directory Configuration

### Default Directory Structure
The application automatically creates these directories relative to the project root:

```
macinvoicer/
├── monitored_invoices/      # Input: PDF files to process
├── output_spreadsheets/     # Output: Monthly Excel files
├── processed_invoices_notes/ # Output: Processing notes
├── logs/                    # Output: Application logs
└── config/                  # Configuration files
```

### Customizing Directory Paths
Currently, directories are defined in `config_loader.py`. For custom paths, modify:

```python
# In config_loader.py
MONITORED_DIR_NAME = "custom_input_folder"
OUTPUT_DIR_NAME = "custom_output_folder"
PROCESSED_NOTES_DIR_NAME = "custom_notes_folder"
LOGS_DIR_NAME = "custom_logs_folder"
```

## Model Selection Guide

### Choosing the Right Model

#### Simple Invoices (Standard Business Invoices)
- **Recommended**: `gpt-4.1-nano`
- **Alternative**: `gpt-4o-mini`
- **Characteristics**: Clear text, standard format, simple structure
- **Cost**: Lowest

#### Standard Invoices (Most Common)
- **Recommended**: `gpt-4o-mini`
- **Alternative**: `gpt-4.1-mini`
- **Characteristics**: Multiple line items, various currencies, moderate complexity
- **Cost**: Low to moderate

#### Complex Invoices (Detailed Analysis Required)
- **Recommended**: `gpt-4o`
- **Alternative**: `o1-mini`
- **Characteristics**: Complex layouts, multiple tables, unusual formats
- **Cost**: Higher but more accurate

#### Reasoning-Heavy Tasks
- **Recommended**: `o1-mini` or `o3-mini`
- **Use Cases**: Invoices requiring interpretation, ambiguous data
- **Cost**: Highest but best accuracy for complex scenarios

### Model Performance Monitoring

Monitor model performance in logs:
```
2025-01-01 12:00:00 - INFO - [abc123] Tokens - Input: 150, Output: 75, Total: 225
2025-01-01 12:00:00 - INFO - [abc123] Cost: $0.000045
2025-01-01 12:00:00 - INFO - [abc123] AI Confidence: 0.9
```

Switch models based on:
- **Cost per invoice**
- **Accuracy requirements**
- **Processing time needs**
- **Volume of invoices**

## Advanced Settings

### Processing Limits

#### Text Length Limits
The application limits PDF text to prevent excessive costs:
- **Default**: 12,000 characters
- **Reason**: Balance between completeness and cost
- **Modification**: Edit `max_text_length` in `openai_ai.py`

#### Confidence Thresholds
Configure minimum confidence for spreadsheet inclusion:
```python
# In main.py, modify the condition:
if extracted_data and extracted_data.get("ai_confidence_score", 0) >= 0.7:
    append_to_spreadsheet(extracted_data, filename)
```

### API Settings

#### Request Parameters
Modify API request parameters in `openai_ai.py`:
```python
# Temperature (0.0 = deterministic, 1.0 = creative)
"temperature": 0.1,

# Maximum output tokens
"max_output_tokens": 1000,

# Store responses for analysis
"store": True,
```

#### Timeout Configuration
Add timeout settings for robust operation:
```python
# In openai_ai.py, modify client initialization
client = openai.OpenAI(
    api_key=OPENAI_API_KEY,
    timeout=30.0  # 30 second timeout
)
```

## Configuration Validation

### Basic Validation Script
Create a validation script to test configuration:

```python
# config_validator.py
import os
from src.config_loader import *
from src.ai_handler.openai_ai import client

def validate_config():
    issues = []
    
    # Check API key
    if not OPENAI_API_KEY:
        issues.append("OPENAI_API_KEY is missing")
    
    # Check model
    if not OPENAI_MODEL:
        issues.append("OPENAI_MODEL is missing")
    
    # Check Tesseract
    if TESSERACT_CMD_PATH and not os.path.exists(TESSERACT_CMD_PATH):
        issues.append(f"Tesseract not found at {TESSERACT_CMD_PATH}")
    
    # Check Poppler
    if POPPLER_PATH and not os.path.exists(POPPLER_PATH):
        issues.append(f"Poppler not found at {POPPLER_PATH}")
    
    # Check OpenAI client
    if not client:
        issues.append("OpenAI client not initialized")
    
    return issues

if __name__ == "__main__":
    issues = validate_config()
    if issues:
        print("Configuration issues found:")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("Configuration is valid!")
```

Run validation:
```bash
cd macinvoicer
python config_validator.py
```

## Environment-Specific Configurations

### Development Configuration
```env
# Development settings
OPENAI_API_KEY=your_dev_api_key
OPENAI_MODEL=gpt-4.1-nano  # Cost-effective for testing
```

### Testing Configuration
```env
# Testing with dummy AI
# Set AI_PROVIDER = "dummy" in main.py
# No API key needed
```

### Production Configuration
```env
# Production settings
OPENAI_API_KEY=your_prod_api_key
OPENAI_MODEL=gpt-4o-mini  # Balanced performance
```

### Configuration Templates

#### Minimal Configuration
```env
# Minimal .env for quick start
OPENAI_API_KEY=your_api_key_here
```

#### Complete Configuration
```env
# Complete .env with all options
OPENAI_API_KEY=your_api_key_here
OPENAI_MODEL=gpt-4o-mini

# Windows paths
TESSERACT_CMD_PATH=C:\Program Files\Tesseract-OCR\tesseract.exe
POPPLER_PATH=C:\Program Files\poppler\bin

# Additional settings would go here
```

## Security Best Practices

### API Key Security
1. **Never commit** `.env` files to version control
2. **Use different keys** for development and production
3. **Rotate keys regularly**
4. **Monitor usage** for unexpected activity
5. **Restrict permissions** if possible

### File Permissions
```bash
# Secure configuration file
chmod 600 config/.env

# Secure directories
chmod 755 macinvoicer/
chmod 755 monitored_invoices/
chmod 755 output_spreadsheets/
```

## Troubleshooting Configuration

### Common Issues

#### Configuration Not Loading
```bash
# Check file exists
ls -la config/.env

# Check file encoding (should be UTF-8)
file config/.env
```

#### Environment Variables Not Available
```python
# Test in Python
import os
from dotenv import load_dotenv

load_dotenv('config/.env')
print(os.getenv('OPENAI_API_KEY'))
```

#### Path Issues on Windows
- Use forward slashes or escape backslashes
- Use raw strings: `r"C:\Program Files\Tesseract-OCR\tesseract.exe"`
- Ensure no trailing spaces in paths

### Debug Configuration Loading
Add debugging to config_loader.py:
```python
print(f"Loading .env from: {DOTENV_PATH}")
print(f"File exists: {os.path.exists(DOTENV_PATH)}")
print(f"OPENAI_API_KEY loaded: {'Yes' if OPENAI_API_KEY else 'No'}")
print(f"OPENAI_MODEL: {OPENAI_MODEL}")
```

---

**Configuration complete!** Your MacInvoicer instance is now properly configured for optimal performance. 