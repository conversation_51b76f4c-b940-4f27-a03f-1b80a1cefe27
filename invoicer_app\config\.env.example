# MacInvoicer Configuration File
# Copy this file to .env and configure the values for your environment

# =============================================================================
# OpenAI Configuration
# =============================================================================
# Your OpenAI API key (required for AI processing)
OPENAI_API_KEY=your_openai_api_key_here

# OpenAI model to use for invoice processing
# Options: gpt-4o, gpt-4o-mini, gpt-4-turbo, gpt-3.5-turbo
OPENAI_MODEL=gpt-4o-mini

# =============================================================================
# Processing Configuration
# =============================================================================
# Enable hybrid processing (text + file upload for enhanced accuracy)
# Set to true for better accuracy but higher API costs
USE_HYBRID_EXTRACTION=false

# =============================================================================
# VAT Configuration
# =============================================================================
# Comma-separated list of VAT rates (percentages)
VAT_RATES=23,13.5,9,0

# Default VAT rate to use when none is detected
DEFAULT_VAT_RATE=23

# =============================================================================
# OCR and PDF Processing Configuration
# =============================================================================
# Path to Tesseract OCR executable (optional if in system PATH)
# Windows example: C:\Program Files\Tesseract-OCR\tesseract.exe
# Linux/Mac example: /usr/bin/tesseract
TESSERACT_CMD_PATH=

# Path to Poppler utilities directory (optional if in system PATH)
# Windows example: C:\Program Files\poppler-24.02.0\Library\bin
# Linux/Mac example: /usr/bin
POPPLER_PATH=

# =============================================================================
# Installation Notes
# =============================================================================
#
# For PDF orientation detection and OCR functionality, you need:
#
# 1. Tesseract OCR:
#    - Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki
#    - Linux: sudo apt-get install tesseract-ocr
#    - Mac: brew install tesseract
#
# 2. Poppler (for PDF to image conversion):
#    - Windows: Download from https://blog.alivate.com.au/poppler-windows/
#    - Linux: sudo apt-get install poppler-utils
#    - Mac: brew install poppler
#
# 3. If these tools are installed in standard locations or added to your
#    system PATH, you can leave TESSERACT_CMD_PATH and POPPLER_PATH empty.
#
# =============================================================================
# Features Enabled by This Configuration
# =============================================================================
#
# ✅ Automatic PDF orientation detection and correction
# ✅ OCR fallback for scanned PDFs
# ✅ Multi-format document support (PDF, Word, Excel)
# ✅ VAT rate detection and validation
# ✅ Hybrid processing for enhanced accuracy
# ✅ Web-based review interface
# ✅ Export functionality (CSV/XLSX)
#