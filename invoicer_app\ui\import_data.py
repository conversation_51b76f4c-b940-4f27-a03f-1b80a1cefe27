import sqlite3
import pandas as pd
import os
import json
import glob
from datetime import datetime
import zipfile

# Use absolute path to ensure we always use the correct database
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
DATABASE = os.path.join(SCRIPT_DIR, 'data', 'invoices.db')
EXCEL_PATH = os.path.abspath(os.path.join(SCRIPT_DIR, '..', 'output_spreadsheets'))
PDF_PATH = os.path.abspath(os.path.join(SCRIPT_DIR, '..', 'monitored_invoices'))

def import_existing_data():
    """Import data from existing Excel files and link to PDFs. Returns a status dictionary."""
    
    conn = sqlite3.connect(DATABASE)
    excel_files = glob.glob(os.path.join(EXCEL_PATH, '*.xlsx'))
    
    total_rows_imported = 0
    total_rows_skipped_as_existing = 0
    files_found = len(excel_files)
    files_read_successfully = 0
    files_failed_to_read = 0
    
    for excel_file in excel_files:
        print(f"Processing {excel_file}")
        try:
            df = pd.read_excel(excel_file)
            files_read_successfully += 1
            
            rows_in_current_file_imported = 0
            rows_in_current_file_skipped = 0
            
            for _, row in df.iterrows():
                filename = row.get('Invoice_Filename', '')
                if not filename:
                    # print(f"Skipping row with no filename in {excel_file}") # Optional: for more verbose logging
                    continue
                
                pdf_path_found = os.path.join(PDF_PATH, filename)
                actual_pdf_path_for_db = pdf_path_found
                if not os.path.exists(pdf_path_found):
                    print(f"PDF not found: {pdf_path_found} - importing data anyway")
                    actual_pdf_path_for_db = f"MISSING: {pdf_path_found}"
                
                extracted_data = {
                    'invoice_id': str(row.get('Invoice_ID', '')),
                    'vendor_name': str(row.get('Vendor_Name', '')),
                    'invoice_date': str(row.get('Invoice_Date', '')),
                    'due_date': str(row.get('Due_Date', '')),
                    'total_amount_eur': float(row.get('Total_Amount_EUR', 0)) if pd.notna(row.get('Total_Amount_EUR')) else 0,
                    'subtotal': float(row.get('Subtotal_EUR', 0)) if pd.notna(row.get('Subtotal_EUR')) else 0,
                    'vat_amount_eur': float(row.get('VAT_Amount_EUR', 0)) if pd.notna(row.get('VAT_Amount_EUR')) else 0,
                    'business_unit': str(row.get('Business_Unit', '')),
                    'original_currency': str(row.get('Original_Currency', '')),
                    'original_total_amount': float(row.get('Original_Total_Amount', 0)) if pd.notna(row.get('Original_Total_Amount')) else 0,
                    'ai_confidence_score': float(row.get('AI_Confidence_Score', 0.5)) if pd.notna(row.get('AI_Confidence_Score')) else 0.5,
                    'processing_notes_ref': str(row.get('Processing_Notes_Ref', '')),
                    'is_duplicate': str(row.get('Is_Duplicate', 'false')).lower() in ['true', '1', 'yes']
                }
                
                existing = conn.execute(
                    'SELECT id FROM invoice_reviews WHERE filename = ?', 
                    (filename,)
                ).fetchone()
                
                if not existing:
                    conn.execute('''
                        INSERT INTO invoice_reviews 
                        (filename, pdf_path, extracted_data, status, created_at)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (
                        filename,
                        actual_pdf_path_for_db,
                        json.dumps(extracted_data),
                        'pending',
                        datetime.now()
                    ))
                    # print(f"Imported: {filename} from {excel_file}") # More detailed log if needed
                    rows_in_current_file_imported += 1
                else:
                    # print(f"Already exists: {filename} (from {excel_file})")
                    rows_in_current_file_skipped += 1
            
            total_rows_imported += rows_in_current_file_imported
            total_rows_skipped_as_existing += rows_in_current_file_skipped
        
        except zipfile.BadZipFile as bzfe:
            files_failed_to_read += 1
            print(f"Skipping corrupted Excel file {excel_file}: {bzfe}. This file is damaged and could not be read.")
        except Exception as e:
            files_failed_to_read += 1 
            print(f"Error processing Excel file {excel_file}: {e}")
    
    conn.commit()
    conn.close()
    
    print(f"Import completed!")
    print(f"Excel files found: {files_found}")
    print(f"Excel files read successfully: {files_read_successfully}")
    print(f"Excel files failed to read (e.g. corrupted, other errors): {files_failed_to_read}")
    print(f"Total rows imported: {total_rows_imported}")
    print(f"Total rows skipped (already exist in DB): {total_rows_skipped_as_existing}")

    return {
        "files_found": files_found,
        "files_read_successfully": files_read_successfully,
        "files_failed_to_read": files_failed_to_read,
        "rows_imported": total_rows_imported,
        "rows_skipped_as_existing": total_rows_skipped_as_existing,
    }

def create_sample_data():
    """Create sample data for testing if no Excel files exist"""
    conn = sqlite3.connect(DATABASE)
    
    # Check if we already have data
    existing = conn.execute('SELECT COUNT(*) FROM invoice_reviews').fetchone()[0]
    if existing > 0:
        print(f"Database already contains {existing} records. Skipping sample data creation.")
        conn.close()
        return
    
    sample_data = [
        {
            'filename': 'sample_invoice_1.pdf',
            'pdf_path': '../monitored_invoices/sample_invoice_1.pdf',
            'extracted_data': {
                'invoice_id': 'INV-2024-001',
                'vendor_name': 'Sample Vendor Ltd',
                'invoice_date': '2024-01-15',
                'due_date': '2024-02-15',
                'total_amount_eur': 1250.00,
                'vat_amount_eur': 250.00,
                'business_unit': 'IT Services',
                'original_currency': 'EUR',
                'original_total_amount': 1250.00,
                'ai_confidence_score': 0.85
            }
        },
        {
            'filename': 'sample_invoice_2.pdf',
            'pdf_path': '../monitored_invoices/sample_invoice_2.pdf',
            'extracted_data': {
                'invoice_id': 'INV-2024-002',
                'vendor_name': 'Tech Solutions Inc',
                'invoice_date': '2024-01-20',
                'due_date': '2024-02-20',
                'total_amount_eur': 850.50,
                'vat_amount_eur': 170.10,
                'business_unit': 'Marketing',
                'original_currency': 'EUR',
                'original_total_amount': 850.50,
                'ai_confidence_score': 0.92
            }
        }
    ]
    
    for data in sample_data:
        conn.execute('''
            INSERT INTO invoice_reviews 
            (filename, pdf_path, extracted_data, status, created_at)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            data['filename'],
            data['pdf_path'],
            json.dumps(data['extracted_data']),
            'pending',
            datetime.now()
        ))
    
    conn.commit()
    conn.close()
    print(f"Created {len(sample_data)} sample records for testing")

if __name__ == '__main__':
    # Try to import existing data first
    import_existing_data()
    
    # If no data was imported, create sample data
    conn = sqlite3.connect(DATABASE)
    count = conn.execute('SELECT COUNT(*) FROM invoice_reviews').fetchone()[0]
    conn.close()
    
    if count == 0:
        print("\nNo data imported. Creating sample data for testing...")
        create_sample_data() 