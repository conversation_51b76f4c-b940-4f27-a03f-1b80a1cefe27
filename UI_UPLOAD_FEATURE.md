# Upload & Processing Control Feature

This document describes the new Upload & Processing Control interface that has been added to the MacInvoicer web UI.

## Overview

The Upload & Processing page provides a comprehensive interface for:
- Uploading PDF invoice files to the monitored directory
- Starting and stopping the main processing app
- Monitoring the status of the processing system
- Managing files in the monitored directory

## Features

### 1. File Upload
- **Drag & Drop Support**: Simply drag PDF files onto the upload area
- **Browse & Select**: Click to open file browser and select multiple PDFs
- **File Validation**: Only PDF files are accepted (16MB max per file)
- **Bulk Upload**: Upload multiple files simultaneously
- **Progress Tracking**: Real-time upload progress indication
- **Duplicate Handling**: Automatic filename conflict resolution

### 2. Processing Control
- **Status Monitoring**: Real-time display of processing app status
- **Start/Stop Controls**: Easy buttons to start and stop the main processing app
- **Auto-refresh**: Status updates every 10 seconds automatically
- **Cross-platform**: Works on both Windows (PowerShell) and Unix systems
- **Virtual Environment Support**: Automatically detects and uses .venv Python

### 3. File Management
- **Directory Listing**: View all files currently in monitored directory
- **File Information**: Shows file size, creation date, and modification time
- **Delete Function**: Remove files from monitored directory with confirmation
- **Auto-refresh**: File list updates every 15 seconds

## Navigation

The interface includes navigation between two main sections:
- **Review Dashboard**: The original invoice review interface
- **Upload & Processing**: The new upload and control interface

## Technical Implementation

### Backend Routes
- `GET /upload` - Upload page template
- `POST /api/upload` - Handle file uploads
- `GET /api/processing/status` - Get processing app status
- `POST /api/processing/start` - Start processing app
- `POST /api/processing/stop` - Stop processing app
- `GET /api/monitored-files` - List files in monitored directory
- `DELETE /api/monitored-files/<filename>` - Delete specific file

### Security Features
- **Secure Filenames**: Uses `secure_filename()` to prevent directory traversal
- **File Type Validation**: Only allows PDF file uploads
- **Size Limits**: 16MB maximum file size per upload
- **CSRF Protection**: Session-based security for form submissions

### Cross-Platform Process Management
- **Windows**: Uses PowerShell commands to manage Python processes
- **Unix/Linux**: Uses standard Unix process management tools
- **Virtual Environment**: Automatically detects and uses virtual environment Python

## Usage Workflow

1. **Navigate to Upload Page**: Click "Upload & Processing" in the navigation
2. **Check Processing Status**: View current status of the processing app
3. **Upload Files**: Drag PDF files to upload area or use browse button
4. **Start Processing**: Click "Start Processing" to begin monitoring
5. **Monitor Progress**: Watch as files are processed automatically
6. **Review Results**: Navigate to Review Dashboard to see extracted data

## File Flow

```
PDF Upload → monitored_invoices/ → Processing App → Extracted Data → Review Dashboard
```

## Error Handling

- **Upload Errors**: Displayed with specific error messages
- **Processing Errors**: Status updates reflect any processing issues
- **Network Errors**: Graceful degradation with user notifications
- **File Conflicts**: Automatic renaming of duplicate filenames

## Browser Compatibility

The interface uses modern JavaScript features and is compatible with:
- Chrome 90+
- Firefox 88+
- Edge 90+
- Safari 14+

## Configuration

No additional configuration is required. The interface automatically:
- Detects the correct monitored directory path
- Finds the virtual environment Python executable
- Adapts to the operating system (Windows/Unix)
- Uses existing Flask configuration settings 