import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))
from unittest import mock
import invoice_parser

def test_ocr_image_with_valid_image():
    """
    Test ocr_image with a valid image path (mocked).
        """
    print("Test: ocr_image should return text for a valid image (mocked).")
    with mock.patch('invoice_parser.pytesseract.image_to_string', return_value='Invoice Text'):
        result = invoice_parser.ocr_image('dummy_path.png')
        assert result == 'Invoice Text'
        print(f"OCR result: {result}")

def test_extract_text_from_pdf_with_valid_pdf():
    """
    Test extract_text_from_pdf with a valid PDF path (mocked).
    """
    print("Test: extract_text_from_pdf should return text for a valid PDF (mocked).")
    with mock.patch('invoice_parser.PyPDF2.PdfReader') as mock_reader:
        mock_reader.return_value.pages = [mock.Mock(extract_text=mock.Mock(return_value='PDF Text'))]
        result = invoice_parser.extract_text_from_pdf('dummy.pdf')
        assert result == 'PDF Text'
        print(f"PDF extraction result: {result}")

def test_extract_text_from_document_pdf():
    """
    Test extract_text_from_document with a PDF file (should use PDF pipeline).
    """
    print("Test: extract_text_from_document should handle PDF files.")
    with mock.patch('invoice_parser.extract_text_from_pdf', return_value='PDF Content') as mock_pdf:
        with mock.patch('os.path.exists', return_value=True):
            result = invoice_parser.extract_text_from_document('test.pdf')
            assert result == 'PDF Content'
            mock_pdf.assert_called_once_with('test.pdf')
            print(f"Document extraction result for PDF: {result}")

def test_extract_text_from_document_docx():
    """
    Test extract_text_from_document with a Word document.
    """
    print("Test: extract_text_from_document should handle DOCX files.")
    with mock.patch('invoice_parser.get_document_text', return_value='Word Content') as mock_doc:
        with mock.patch('invoice_parser.preprocess_ocr_text', return_value='Processed Word Content') as mock_preprocess:
            with mock.patch('os.path.exists', return_value=True):
                result = invoice_parser.extract_text_from_document('test.docx')
                assert result == 'Processed Word Content'
                mock_doc.assert_called_once_with('test.docx')
                mock_preprocess.assert_called_once_with('Word Content')
                print(f"Document extraction result for DOCX: {result}")

def test_extract_text_from_document_xlsx():
    """
    Test extract_text_from_document with an Excel document.
    """
    print("Test: extract_text_from_document should handle XLSX files.")
    with mock.patch('invoice_parser.get_document_text', return_value='Excel Content') as mock_excel:
        with mock.patch('invoice_parser.preprocess_ocr_text', return_value='Processed Excel Content') as mock_preprocess:
            with mock.patch('os.path.exists', return_value=True):
                result = invoice_parser.extract_text_from_document('test.xlsx')
                assert result == 'Processed Excel Content'
                mock_excel.assert_called_once_with('test.xlsx')
                mock_preprocess.assert_called_once_with('Excel Content')
                print(f"Document extraction result for XLSX: {result}")

def test_extract_text_from_document_unsupported():
    """
    Test extract_text_from_document with an unsupported file type.
    """
    print("Test: extract_text_from_document should reject unsupported file types.")
    with mock.patch('os.path.exists', return_value=True):
        result = invoice_parser.extract_text_from_document('test.txt')
        assert result is None
        print("Unsupported file type correctly rejected.")

def test_extract_text_from_document_nonexistent():
    """
    Test extract_text_from_document with a non-existent file.
    """
    print("Test: extract_text_from_document should handle non-existent files.")
    with mock.patch('os.path.exists', return_value=False):
        result = invoice_parser.extract_text_from_document('nonexistent.pdf')
        assert result is None
        print("Non-existent file correctly handled.")

def test_extract_text_from_document_extraction_failure():
    """
    Test extract_text_from_document when document text extraction fails.
    """
    print("Test: extract_text_from_document should handle extraction failures.")
    with mock.patch('invoice_parser.get_document_text', return_value=None):
        with mock.patch('os.path.exists', return_value=True):
            result = invoice_parser.extract_text_from_document('test.docx')
            assert result is None
            print("Extraction failure correctly handled.")

if __name__ == '__main__':
    print("Running invoice parser tests...")
    test_ocr_image_with_valid_image()
    test_extract_text_from_pdf_with_valid_pdf()
    test_extract_text_from_document_pdf()
    test_extract_text_from_document_docx()
    test_extract_text_from_document_xlsx()
    test_extract_text_from_document_unsupported()
    test_extract_text_from_document_nonexistent()
    test_extract_text_from_document_extraction_failure()
    print("All tests passed!")