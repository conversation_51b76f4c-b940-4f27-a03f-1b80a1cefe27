# PDF Orientation Detection and Correction

## Overview

MacInvoicer now includes automatic PDF orientation detection and correction during the import/upload stage. This feature ensures that all PDFs are properly oriented for optimal readability in the web viewer, eliminating the need for manual rotation.

## How It Works

### 1. **Automatic Detection**
When a PDF is uploaded or processed:
1. The system converts the first 1-2 pages to images
2. OCR analysis is performed at 0°, 90°, 180°, and 270° rotations
3. Text confidence, word count, and line structure are analyzed
4. The orientation with the highest composite score is selected

### 2. **Smart Scoring Algorithm**
The system uses a weighted scoring formula:
- **OCR Confidence (50%)**: How confident the OCR engine is about the text
- **Text Length (30%)**: Amount of readable text detected
- **Word Count (15%)**: Number of distinct words found
- **Line Structure (5%)**: Number of text lines detected

### 3. **Automatic Correction**
If a better orientation is detected:
- The PDF is automatically rotated to the correct orientation
- The corrected PDF is stored in the database
- The original file remains unchanged
- Temporary files are cleaned up automatically

## Configuration

### Required Dependencies
The orientation detection requires these tools (already in requirements.txt):
- **Tesseract OCR**: For text extraction and confidence analysis
- **Poppler**: For PDF to image conversion
- **PyPDF2**: For PDF rotation
- **Pillow**: For image processing

### Environment Configuration
Add these optional settings to your `.env` file:

```bash
# Path to Tesseract executable (if not in system PATH)
TESSERACT_CMD_PATH=C:\Program Files\Tesseract-OCR\tesseract.exe

# Path to Poppler utilities (if not in system PATH)  
POPPLER_PATH=C:\Program Files\poppler-24.02.0\Library\bin
```

### Installation Instructions

#### Windows
1. **Tesseract**: Download from [UB-Mannheim/tesseract](https://github.com/UB-Mannheim/tesseract/wiki)
2. **Poppler**: Download from [Poppler for Windows](https://blog.alivate.com.au/poppler-windows/)

#### Linux
```bash
sudo apt-get install tesseract-ocr poppler-utils
```

#### macOS
```bash
brew install tesseract poppler
```

## Features

### ✅ **Automatic Processing**
- No manual intervention required
- Works during normal upload/import workflow
- Transparent to the user

### ✅ **Smart Detection**
- Analyzes multiple pages for accuracy
- Uses composite scoring for reliability
- Only rotates when significant improvement is detected (20% threshold)

### ✅ **Performance Optimized**
- Analyzes only first 1-2 pages for speed
- Efficient OCR processing
- Minimal impact on processing time

### ✅ **Error Handling**
- Graceful fallback to original PDF if detection fails
- Comprehensive logging for troubleshooting
- No data loss if orientation correction fails

## Logging and Monitoring

The system provides detailed logging for orientation detection:

```
INFO: Checking PDF orientation for: invoice_sample.pdf
INFO: Rotation 0°: Average score = 45.2
INFO: Rotation 90°: Average score = 78.9
INFO: Rotation 180°: Average score = 12.1
INFO: Rotation 270°: Average score = 23.4
INFO: Best orientation for invoice_sample.pdf: 90° (score: 78.9)
INFO: Orientation correction recommended: 90° rotation (improvement: 74.8%)
INFO: PDF orientation corrected: invoice_sample.pdf rotated 90°
```

## Testing

Use the included test script to verify orientation detection:

```bash
cd invoicer_app
python test_orientation.py monitored_invoices/sample_invoice.pdf
```

The test script will:
1. Analyze the PDF orientation
2. Apply correction if needed
3. Show before/after file sizes
4. Clean up temporary files

## Integration Points

### 1. **Upload Processing**
- Automatic detection during file upload via web interface
- Integrated into the main processing pipeline

### 2. **File Monitoring**
- Works with the file system monitoring for dropped files
- Processes PDFs as they are added to monitored_invoices/

### 3. **Database Storage**
- Corrected PDFs are stored in the database
- Original file metadata is preserved
- Orientation correction is logged in processing notes

## Performance Impact

- **Minimal**: Adds ~2-5 seconds per PDF for orientation analysis
- **Efficient**: Only processes first 1-2 pages
- **Smart**: Skips correction if improvement is less than 20%
- **Parallel**: Doesn't block other processing operations

## Troubleshooting

### Common Issues

1. **"Tesseract not found"**
   - Install Tesseract OCR
   - Set TESSERACT_CMD_PATH in .env file

2. **"Poppler not found"**
   - Install Poppler utilities
   - Set POPPLER_PATH in .env file

3. **"No orientation improvement detected"**
   - PDF is already correctly oriented
   - OCR confidence is too low to determine orientation
   - This is normal behavior

### Debug Mode
Enable detailed logging by checking the application logs in `logs/app.log` for orientation detection details.

## Benefits

### 🎯 **User Experience**
- PDFs always display correctly in the web viewer
- No manual rotation needed
- Consistent viewing experience

### 🚀 **Efficiency**
- Automatic processing saves time
- Reduces manual review effort
- Improves data extraction accuracy

### 🔧 **Reliability**
- Robust detection algorithm
- Fallback to original if correction fails
- Comprehensive error handling

## Future Enhancements

Potential future improvements:
- Batch orientation correction for existing PDFs
- User preference settings for detection sensitivity
- Support for mixed-orientation multi-page documents
- Integration with document conversion pipeline
