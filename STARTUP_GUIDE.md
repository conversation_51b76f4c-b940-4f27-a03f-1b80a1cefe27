# MacInvoicer Startup Guide

This guide explains how to start the MacInvoicer application using the provided startup scripts.

## Overview

MacInvoicer consists of two main components:
1. **Main Process** (`main.py`) - Monitors for new invoice files and processes them automatically
2. **Web UI** (`app.py`) - Provides a web interface for reviewing and managing processed invoices

Both components need to run simultaneously for the full functionality.

## Prerequisites

1. **Virtual Environment**: Ensure you have a virtual environment set up with all dependencies installed:
   ```bash
   python -m venv .venv
   .venv\Scripts\activate  # On Windows
   # or
   source .venv/bin/activate  # On Unix/Linux/Mac
   pip install -r invoicer_app/requirements.txt
   ```

2. **Configuration**: Make sure your configuration files are properly set up (check `invoicer_app/config/` directory).

## Startup Options

You have three different startup scripts to choose from:

### Option 1: PowerShell Script (Recommended for Windows)

```powershell
.\start_macinvoicer.ps1
```

**Features:**
- Starts main.py in a separate window
- Runs app.py in the current window
- Provides colored output and status messages
- Includes error checking and validation

### Option 2: Batch File (Windows Command Prompt)

```cmd
start_macinvoicer.bat
```

**Features:**
- Simple batch file execution
- Starts main.py in a new command window
- Runs app.py in the current window
- Basic error checking

### Option 3: Python Script (Cross-platform)

```bash
python start_macinvoicer.py
```

**Features:**
- Cross-platform compatibility (Windows, Linux, Mac)
- Colored console output
- Graceful shutdown handling (Ctrl+C)
- Process management and cleanup

## What Happens When You Start

1. **Validation**: The script checks for:
   - Virtual environment existence
   - Required Python scripts (main.py, app.py)
   - Proper directory structure

2. **Main Process Start**: 
   - Activates the virtual environment
   - Starts `main.py` in a separate window/process
   - This begins monitoring the `monitored_invoices` folder for new PDF files

3. **Web UI Start**:
   - Waits 3 seconds for main.py to initialize
   - Starts the Flask web application (`app.py`)
   - Web interface becomes available at `http://127.0.0.1:5000`

## Using the Application

1. **Upload Invoices**: 
   - Use the web interface at `http://127.0.0.1:5000/upload`
   - Or directly copy PDF files to the `invoicer_app/monitored_invoices/` folder

2. **Review Processing**:
   - The main process will automatically detect and process new files
   - Check the web interface to review extracted data
   - Approve or reject processed invoices

3. **Monitor Logs**:
   - Main process logs appear in its separate window
   - Flask app logs appear in the startup window
   - Check log files in the application directory for detailed information

## Stopping the Application

- **Ctrl+C** in the window running the startup script will stop the web UI
- Close the separate main.py window to stop file monitoring
- The Python script version handles graceful shutdown of both processes

## Troubleshooting

### Common Issues:

1. **Virtual Environment Not Found**:
   ```
   ERROR: Virtual environment not found at: .venv
   ```
   **Solution**: Create and activate virtual environment, then install requirements.

2. **Script Files Not Found**:
   ```
   ERROR: main.py not found at: invoicer_app/src/main.py
   ```
   **Solution**: Ensure you're running the startup script from the project root directory.

3. **Permission Issues (PowerShell)**:
   ```
   Execution of scripts is disabled on this system
   ```
   **Solution**: Run `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser` in PowerShell.

4. **Port Already in Use**:
   ```
   Address already in use
   ```
   **Solution**: Stop any existing Flask applications or change the port in `app.py`.

### Getting Help

- Check the console output for detailed error messages
- Review log files in the application directory
- Ensure all configuration files are properly set up
- Verify that all required dependencies are installed in the virtual environment

## Manual Startup (Alternative)

If the startup scripts don't work, you can start the components manually:

1. **Terminal 1** (Main Process):
   ```bash
   .venv\Scripts\activate
   cd invoicer_app\src
   python main.py
   ```

2. **Terminal 2** (Web UI):
   ```bash
   .venv\Scripts\activate
   cd invoicer_app\ui
   python app.py
   ```

Then open `http://127.0.0.1:5000` in your web browser.
