#!/usr/bin/env python3
"""
tree_to_md.py

Creates a directory tree structure for invoicer_app and below, 
and saves it to a markdown file.
"""

import os
from pathlib import Path
from datetime import datetime


def generate_tree(directory, prefix="", is_last=False, max_depth=None, current_depth=0):
    """
    Generate a tree structure for the given directory.
    
    Args:
        directory (Path): Directory to process
        prefix (str): Current indentation prefix
        is_last (bool): Whether this is the last item in the current level
        max_depth (int): Maximum depth to traverse (None for unlimited)
        current_depth (int): Current traversal depth
    
    Returns:
        list: List of strings representing the tree structure
    """
    if max_depth is not None and current_depth >= max_depth:
        return []
    
    tree_lines = []
    
    # Get directory name
    dir_name = directory.name if directory.name else str(directory)
    
    # Add current directory to tree
    connector = "└── " if is_last else "├── "
    tree_lines.append(f"{prefix}{connector}{dir_name}/")
    
    # Prepare prefix for children
    child_prefix = prefix + ("    " if is_last else "│   ")
    
    try:
        # Get all items in directory, sorted
        items = sorted([item for item in directory.iterdir() if not item.name.startswith('.')], 
                      key=lambda x: (x.is_file(), x.name.lower()))
        
        # Process each item
        for i, item in enumerate(items):
            is_last_item = (i == len(items) - 1)
            
            if item.is_dir():
                # Recursively process subdirectories
                subtree = generate_tree(item, child_prefix, is_last_item, max_depth, current_depth + 1)
                tree_lines.extend(subtree)
            else:
                # Add file to tree
                connector = "└── " if is_last_item else "├── "
                # Get file size
                try:
                    size = item.stat().st_size
                    if size < 1024:
                        size_str = f"{size}B"
                    elif size < 1024 * 1024:
                        size_str = f"{size/1024:.1f}KB"
                    else:
                        size_str = f"{size/(1024*1024):.1f}MB"
                    tree_lines.append(f"{child_prefix}{connector}{item.name} ({size_str})")
                except (OSError, PermissionError):
                    tree_lines.append(f"{child_prefix}{connector}{item.name}")
    
    except PermissionError:
        tree_lines.append(f"{child_prefix}[Permission Denied]")
    
    return tree_lines


def create_tree_markdown():
    """Create the directory tree markdown file."""
    # Define paths
    base_dir = Path("invoicer_app")
    output_file = Path("directory_tree.md")
    
    if not base_dir.exists():
        print(f"Error: Directory '{base_dir}' not found.")
        return
    
    # Generate the tree
    print(f"Generating directory tree for '{base_dir}'...")
    tree_lines = generate_tree(base_dir)
    
    # Create markdown content
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    markdown_content = f"""# Directory Tree for invoicer_app

Generated on: {timestamp}

```
{chr(10).join(tree_lines)}
```

---
*This file was automatically generated by tree_to_md.py*
"""
    
    # Write to file
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        print(f"Directory tree saved to '{output_file}'")
        print(f"Total items processed: {len(tree_lines)}")
        
    except Exception as e:
        print(f"Error writing to file: {e}")


if __name__ == "__main__":
    create_tree_markdown() 