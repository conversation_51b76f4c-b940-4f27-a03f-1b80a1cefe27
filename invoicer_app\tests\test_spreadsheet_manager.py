import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))
import tempfile
import shutil
import pandas as pd
import spreadsheet_manager

def test_get_monthly_spreadsheet_path():
    """
    Test that get_monthly_spreadsheet_path returns a valid file path for the current month.
    """
    print("Test: get_monthly_spreadsheet_path should return a valid path for the current month.")
    path = spreadsheet_manager.get_monthly_spreadsheet_path()
    assert path.endswith('.xlsx')
    print(f"Spreadsheet path: {path}")

def test_append_to_spreadsheet_creates_and_appends():
    """
    Test that append_to_spreadsheet creates a new spreadsheet if it doesn't exist and appends data.
    """
    print("Test: append_to_spreadsheet should create and append to spreadsheet.")
    temp_dir = tempfile.mkdtemp()
    file_path = os.path.join(temp_dir, "test.xlsx")
    data = {"Invoice Number": "123", "Amount": 100.0}
    spreadsheet_manager.append_to_spreadsheet(data, file_path)
    assert os.path.exists(file_path)
    df = pd.read_excel(file_path)
    assert "Invoice Number" in df.columns
    assert df.iloc[0]["Invoice Number"] == "123"
    print(f"Spreadsheet created and data appended: {df.to_dict()}")
    shutil.rmtree(temp_dir) 