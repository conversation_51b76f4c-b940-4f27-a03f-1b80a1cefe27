import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))
import logging
import tempfile
import app_logger

def test_setup_logger_creates_log_file():
    """
    Test that setup_logger creates a log file and returns a logger instance.
    """
    print("Test: setup_logger should create a log file and return a logger instance.")
    temp_dir = tempfile.mkdtemp()
    log_file = os.path.join(temp_dir, "test.log")
    logger = app_logger.setup_logger(name="TestLogger", log_level=logging.INFO)
    logger.info("Test log entry.")
    # Check if log file is created
    log_files = [f for f in os.listdir(temp_dir) if f.endswith('.log')]
    assert log_files, "No log file created."
    print(f"Log file(s) created: {log_files}")

def test_setup_logger_log_level():
    """
    Test that setup_logger sets the correct log level.
    """
    print("Test: setup_logger should set the correct log level.")
    logger = app_logger.setup_logger(name="TestLogger2", log_level=logging.WARNING)
    assert logger.level == logging.WARNING
    print(f"Logger level is: {logger.level}") 