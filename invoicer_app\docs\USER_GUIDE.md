# MacInvoicer User Guide

This guide provides comprehensive instructions for using MacInvoicer to process invoice files automatically.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Basic Operation](#basic-operation)
3. [Invoice Processing Workflow](#invoice-processing-workflow)
4. [Understanding Output](#understanding-output)
5. [Monitoring and Logs](#monitoring-and-logs)
6. [Cost Management](#cost-management)
7. [Troubleshooting](#troubleshooting)
8. [Best Practices](#best-practices)

## Getting Started

### Prerequisites
Before using MacInvoicer, ensure you have:
- Completed the installation process (see [Installation Guide](INSTALLATION.md))
- Configured your environment variables (see [Configuration Guide](CONFIGURATION.md))
- A valid OpenAI API key with sufficient credits
- PDF invoices ready for processing

### Quick Start
1. **Navigate to the application directory**:
   ```bash
   cd macinvoicer/src
   ```

2. **Start the application**:
   ```bash
   python main.py
   ```

3. **Add invoices for processing**:
   - Copy PDF files to the `monitored_invoices/` directory
   - The application will automatically detect and process them

4. **Monitor progress**:
   - Watch the console output for processing status
   - Check `logs/app.log` for detailed information

5. **Review results**:
   - Monthly spreadsheets appear in `output_spreadsheets/`
   - Processing notes are saved in `processed_invoices_notes/`

## Basic Operation

### Starting MacInvoicer

#### Standard Start
```bash
cd macinvoicer/src
python main.py
```

#### With Virtual Environment
```bash
cd macinvoicer
source .venv/bin/activate  # Linux/macOS
.venv\Scripts\activate     # Windows
cd src
python main.py
```

### Expected Startup Behavior
When MacInvoicer starts successfully, you'll see:
```
2025-01-01 12:00:00 - INFO - Application starting...
2025-01-01 12:00:00 - INFO - Monitoring directory: /path/to/monitored_invoices
2025-01-01 12:00:00 - INFO - Using AI Provider: openai
```

### Stopping the Application
- Press `Ctrl+C` to stop monitoring gracefully
- The application will finish processing any current files before stopping

## Invoice Processing Workflow

### 1. File Detection
- MacInvoicer monitors the `monitored_invoices/` directory continuously
- When a new PDF file is added, processing begins automatically
- Only PDF files are processed; other file types are ignored

### 2. Text Extraction
MacInvoicer uses a two-tier text extraction approach:

#### Primary Method: PyPDF2
- Directly extracts text from text-based PDFs
- Fast and efficient for standard business invoices
- Works best with electronically generated PDFs

#### Fallback Method: OCR
- Converts PDF pages to images
- Uses Tesseract OCR to extract text from images
- Activated when PyPDF2 extracts less than 100 characters
- Essential for scanned invoices or image-based PDFs

### 3. AI-Powered Data Extraction
- Extracted text is sent to OpenAI for structured data extraction
- AI identifies and extracts key invoice fields:
  - Invoice ID/Number
  - Vendor name
  - Invoice date
  - Due date
  - Total amount
  - VAT/tax amount
  - Currency

### 4. Data Processing
- Extracted data is validated and formatted
- Dates are standardized to YYYY-MM-DD format
- Amounts are converted to appropriate numeric formats
- Missing fields are marked as "N/A" or null

### 5. Output Generation
Two types of output are created:

#### Spreadsheet Entry
- Data is appended to the current month's Excel file
- Running totals are automatically calculated
- File format: `YYYY-MM_invoices.xlsx`

#### Processing Note
- Detailed processing information is saved
- Includes AI confidence scores and any issues
- File format: `filename_processing_note.txt`

## Understanding Output

### Monthly Spreadsheets

#### File Naming
- Format: `YYYY-MM_invoices.xlsx`
- Examples: `2025-01_invoices.xlsx`, `2025-02_invoices.xlsx`
- New file created automatically each month

#### Column Structure
| Column | Description | Example |
|--------|-------------|---------|
| Processing_DateTime | When the invoice was processed | 2025-01-15 14:30:22 |
| Invoice_Filename | Original PDF filename | invoice_001.pdf |
| Invoice_ID | Extracted invoice number | INV-2025-001 |
| Vendor_Name | Company/vendor name | Acme Corp Ltd |
| Invoice_Date | Date on the invoice | 2025-01-10 |
| Due_Date | Payment due date | 2025-02-10 |
| Total_Amount_EUR | Total amount in EUR | 1250.50 |
| VAT_Amount_EUR | VAT amount in EUR | 250.10 |
| Original_Currency | Currency from invoice | USD |
| Original_Total_Amount | Amount in original currency | 1350.75 |
| AI_Confidence_Score | AI extraction confidence (0-1) | 0.95 |
| Running_Total_EUR | Cumulative total for month | 5420.80 |

#### Running Totals
- Automatically calculated as invoices are processed
- Provides month-to-date totals
- Recalculated when new invoices are added
- Useful for budget tracking and financial reporting

### Processing Notes

Each processed invoice generates a detailed note file:

#### File Naming
- Format: `[original_filename]_processing_note.txt`
- Example: `invoice_001_processing_note.txt`

#### Content Structure
```
Invoice: invoice_001.pdf
Processing Time: 2025-01-15 14:30:22
AI Provider: openai
AI Confidence: 0.95
Extracted Data:
  invoice_id: INV-2025-001
  vendor_name: Acme Corp Ltd
  invoice_date: 2025-01-10
  due_date: 2025-02-10
  total_amount_eur: 1250.50
  vat_amount_eur: 250.10
  original_currency: USD
  original_total_amount: 1350.75
  ai_confidence_score: 0.95
  processing_notes: Processed via OpenAI Responses API...

Notes: Successfully processed. High confidence extraction.
```

## Monitoring and Logs

### Console Output
MacInvoicer provides real-time feedback through console messages:

#### Normal Processing
```
===========================================================
Processing new invoice: /path/to/invoice_001.pdf
[abc12345] Starting OpenAI Responses API processing for invoice_001.pdf
[abc12345] Responses API completed successfully
[abc12345] Cost: $0.000045
Successfully extracted data for invoice_001.pdf
Data for invoice_001.pdf appended to 2025-01_invoices.xlsx
```

#### Error Scenarios
```
WARNING - Could not extract text from invoice_002.pdf. Skipping.
ERROR - OpenAI API key not configured. Cannot use OpenAI provider.
ERROR - Rate limit exceeded. Retry later.
```

### Log Files
Detailed logs are saved to `logs/app.log`:

#### Log Entry Format
```
2025-01-15 14:30:22 - MacInvoicerApp - INFO - main:25 - Processing new invoice: invoice_001.pdf
2025-01-15 14:30:23 - MacInvoicerApp - INFO - openai_ai:174 - [abc12345] Starting OpenAI processing
2025-01-15 14:30:25 - MacInvoicerApp - INFO - openai_ai:279 - [abc12345] USAGE_DATA: {"tokens": 225, "cost": 0.000045}
```

#### Log Analysis
Monitor logs for:
- **Processing success rates**
- **API costs and token usage**
- **Error patterns**
- **Performance metrics**

## Cost Management

### Understanding Costs
OpenAI charges based on token usage:
- **Input tokens**: Text sent to the AI (invoice content)
- **Output tokens**: Structured data returned by AI
- **Total cost**: (Input tokens × Input rate) + (Output tokens × Output rate)

### Cost Monitoring
MacInvoicer provides detailed cost tracking:

#### Real-time Cost Display
```
[abc12345] Tokens - Input: 150, Output: 75, Total: 225
[abc12345] Cost: $0.000045
```

#### Usage Data Logs
```json
{
  "timestamp": "2025-01-15T14:30:25",
  "session_id": "abc12345",
  "filename": "invoice_001.pdf",
  "model": "gpt-4.1-nano",
  "tokens": {"input": 150, "output": 75, "total": 225},
  "cost_usd": 0.000045
}
```

### Cost Optimization Strategies

#### 1. Model Selection
- Use `gpt-4.1-nano` for simple invoices ($0.00005 input / $0.0002 output per 1K tokens)
- Upgrade to `gpt-4o-mini` for better accuracy ($0.00015 input / $0.0006 output per 1K tokens)
- Reserve `gpt-4o` for complex cases ($0.005 input / $0.015 output per 1K tokens)

#### 2. Text Length Management
- MacInvoicer automatically truncates very long texts (>12,000 chars)
- OCR may produce longer text than PyPDF2
- Monitor input token usage in logs

#### 3. Batch Processing
- Process multiple invoices during off-peak hours
- Monitor monthly spending patterns
- Set up alerts for unusual cost spikes

## Troubleshooting

### Common Issues

#### No Files Being Processed
**Symptoms**: Application runs but doesn't process PDFs
**Solutions**:
1. Check if files are actually PDF format
2. Verify file permissions (readable by application)
3. Ensure files are copied (not moved while being written)

#### Text Extraction Failures
**Symptoms**: "Could not extract text" warnings
**Solutions**:
1. Verify Tesseract installation for OCR fallback
2. Check Poppler installation for PDF-to-image conversion
3. Test with different PDF files to isolate format issues

#### AI Processing Errors
**Symptoms**: API errors or authentication failures
**Solutions**:
1. Verify OpenAI API key is correct and active
2. Check API credit balance
3. Verify network connectivity
4. Review rate limiting in logs

#### Spreadsheet Issues
**Symptoms**: Data not appearing in Excel files
**Solutions**:
1. Check write permissions for output directory
2. Ensure Excel files aren't open in other applications
3. Verify pandas and openpyxl dependencies

### Debug Mode
Enable additional logging by modifying `app_logger.py`:
```python
app_logger = setup_logger(log_level=logging.DEBUG)
```

## Best Practices

### File Management
1. **Consistent Naming**: Use descriptive, consistent filenames
2. **File Organization**: Keep processed files separate from pending ones
3. **Backup Strategy**: Backup both input PDFs and output spreadsheets
4. **Archive Old Files**: Move processed invoices to archive directories

### Quality Control
1. **Regular Review**: Check AI confidence scores in processing notes
2. **Spot Checking**: Manually verify a sample of extractions
3. **Error Pattern Analysis**: Review logs for recurring issues
4. **Model Performance**: Monitor accuracy vs. cost trade-offs

### Security
1. **API Key Rotation**: Regularly rotate OpenAI API keys
2. **Access Control**: Limit access to processing directories
3. **Data Privacy**: Be aware of data sent to OpenAI APIs
4. **Log Security**: Protect log files containing processing details

### Performance Optimization
1. **Scheduled Processing**: Process large batches during off-peak hours
2. **Resource Monitoring**: Monitor CPU and memory usage
3. **Network Stability**: Ensure stable internet for API calls
4. **Storage Management**: Regularly clean up old logs and temporary files

### Financial Management
1. **Budget Setting**: Set monthly API spending limits
2. **Cost Analysis**: Regularly review cost per invoice
3. **Model Selection**: Choose appropriate models for invoice complexity
4. **Usage Tracking**: Monitor token usage trends over time

---

**Happy invoice processing!** MacInvoicer is designed to streamline your invoice management workflow while providing detailed insights and cost control. 