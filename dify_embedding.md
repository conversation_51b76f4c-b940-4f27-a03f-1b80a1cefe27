# Embedding In Websites

Dify Apps can be embedded in websites using an iframe. This allows you to integrate your Dify App into your website, blog, or any other web page.

When use Dify Chatbot Bubble Button embed in your website, you can customize the button style, position, and other settings.

## Customizing the Dify Chatbot Bubble Button

The Dify Chatbot Bubble Button can be customized through the following configuration options:

```javascript
window.difyChatbotConfig = {
    // Required, automatically generated by Dify
    token: 'YOUR_TOKEN',
    // Optional, default is false
    isDev: false,
    // Optional, when isDev is true, default is 'https://dev.udify.app', otherwise default is 'https://udify.app'
    baseUrl: 'YOUR_BASE_URL',
    // Optional, It can accept any valid HTMLElement attribute other than `id`, such as `style`, `className`, etc
    containerProps: {},
    // Optional, If or not the button is allowed to be dragged, default is `false`
    draggable: false,
    // Optional, The axis along which the button is allowed to be dragged, default is `both`, can be `x`, `y`, `both`
    dragAxis: 'both',
    // Optional, An object of inputs that set in the dify chatbot
    inputs: {
        // key is the variable name
        // e.g.
        // name: "NAM<PERSON>"
    }
}
```

## Overriding Default Button Styles

You can override the default button style using CSS variables or the `containerProps` option. Apply these methods based on CSS specificity to achieve your desired customizations.

### 1.Modifying CSS Variables

The following CSS variables are supported for customization:

```css
/* Button distance to bottom, default is `1rem` */
--dify-chatbot-bubble-button-bottom

/* Button distance to right, default is `1rem` */
--dify-chatbot-bubble-button-right

/* Button distance to left, default is `unset` */
--dify-chatbot-bubble-button-left

/* Button distance to top, default is `unset` */
--dify-chatbot-bubble-button-top

/* Button background color, default is `#155EEF` */
--dify-chatbot-bubble-button-bg-color

/* Button width, default is `50px` */
--dify-chatbot-bubble-button-width

/* Button height, default is `50px` */
--dify-chatbot-bubble-button-height

/* Button border radius, default is `25px` */
--dify-chatbot-bubble-button-border-radius

/* Button box shadow, default is `rgba(0, 0, 0, 0.2) 0px 4px 8px 0px)` */
--dify-chatbot-bubble-button-box-shadow

/* Button hover transform, default is `scale(1.1)` */
--dify-chatbot-bubble-button-hover-transform
```

To change the background color to #ABCDEF, add this CSS:

```css
#dify-chatbot-bubble-button {
    --dify-chatbot-bubble-button-bg-color: #ABCDEF;
}
```

### 2.Using `containerProps`

Set inline styles using the `style` attribute:

```javascript
window.difyChatbotConfig = {
    // ... other configurations
    containerProps: {
        style: {
            backgroundColor: '#ABCDEF',
            width: '60px',
            height: '60px',
            borderRadius: '30px',
        },
        // For minor style overrides, you can also use a string value for the `style` attribute:
        // style: 'background-color: #ABCDEF; width: 60px;',
    },
}
```

Apply CSS classes using the `className` attribute:

```javascript
window.difyChatbotConfig = {
    // ... other configurations
    containerProps: {
        className: 'dify-chatbot-bubble-button-custom my-custom-class',
    },
}
```

### 3. Passing `inputs`

There are four types of inputs supported:

1. **`text-input`**: Accepts any value. The input string will be truncated if its length exceeds the maximum allowed length.
2. **`paragraph`**: Similar to `text-input`, it accepts any value and truncates the string if it's longer than the maximum length.
3. **`number`**: Accepts a number or a numerical string. If a string is provided, it will be converted to a number using the `Number` function.
4. **`options`**: Accepts any value, provided it matches one of the pre-configured options.

Example configuration:

```javascript
window.difyChatbotConfig = {
    // Other configuration settings...
    inputs: {
        name: 'apple',
    },
}
```

Note: When using the embed.js script to create an iframe, each input value will be processed—compressed using GZIP and encoded in base64—before being appended to the URL.

For example, the URL with processed input values will look like this:
`http://localhost/chatbot/{token}?name=H4sIAKUlmWYA%2FwWAIQ0AAACDsl7gLuiv2PQEUNAuqQUAAAA%3D`

{/*
  Contributing Section
  DO NOT edit this section!
  It will be automatically generated by the script.
  */}

***

[Edit this page](https://github.com/langgenius/dify-docs/edit/main/en/guides/application-publishing/embedding-in-websites.mdx) | [Report an issue](https://github.com/langgenius/dify-docs/issues/new?title=Documentation%20Issue%3A%20ding-in-websi\&body=%23%23%20Issue%20Description%0A%3C%21--%20Please%20briefly%20describe%20the%20issue%20you%20found%20--%3E%0A%0A%23%23%20Page%20Link%0Ahttps%3A%2F%2Fgithub.com%2Flanggenius%2Fdify-docs%2Fblob%2Fmain%2Fen/guides/application-publishing%2Fembedding-in-websites.mdx%0A%0A%23%23%20Suggested%20Changes%0A%3C%21--%20If%20you%20have%20specific%20suggestions%20for%20changes%2C%20please%20describe%20them%20here%20--%3E%0A%0A%3C%21--%20Thank%20you%20for%20helping%20improve%20our%20documentation%21%20--%3E)
