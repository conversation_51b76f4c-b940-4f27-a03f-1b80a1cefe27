"""
Application logging module for the MacInvoicer tool.

This module provides centralized logging functionality for the entire application.
It sets up both file and console logging with consistent formatting and handles
log directory creation automatically.

Key Features:
    - Unified logging configuration for the entire application
    - Automatic log directory creation
    - File and console output with timestamps
    - UTF-8 encoding support for international characters
    - Configurable log levels
    - Prevention of duplicate handlers

Author: MacInvoicer Development Team
Version: 1.0.0
"""

import logging
import os
# Assuming config_loader is in the same directory or accessible via Python path
# If run directly, this relative import might be an issue, but when main.py runs it, it should be fine.
from config_loader import LOGS_PATH 

LOG_FILE_NAME = "app.log"
LOG_FILE_PATH = os.path.join(LOGS_PATH, LOG_FILE_NAME)

def setup_logger(name='MacInvoicerApp', log_level=logging.DEBUG):
    """
    Sets up and returns a configured logger instance with file and console handlers.
    
    This function creates a logger with both file and console output, ensuring
    consistent formatting across the application. It handles log directory creation
    and prevents duplicate handlers from being added.
    
    Args:
        name (str): The name for the logger instance. Defaults to 'MacInvoicerApp'.
        log_level (int): The logging level (e.g., logging.DEBUG, logging.INFO).
                        Defaults to logging.DEBUG.
                        
    Returns:
        logging.Logger: Configured logger instance ready for use.
        
    Raises:
        OSError: If log directory creation fails.
        Exception: If file handler setup fails (handled gracefully).
        
    Note:
        If the logger already has handlers, they are cleared to prevent
        duplicate log entries. The function is safe to call multiple times.
    """
    
    # Create logs directory if it doesn't exist (config_loader should also do this)
    if not os.path.exists(LOGS_PATH):
        try:
            os.makedirs(LOGS_PATH)
        except OSError as e:
            print(f"Error creating log directory {LOGS_PATH}: {e}")
            # Fallback or raise error if logging is critical

    logger = logging.getLogger(name)
    
    # Prevent adding multiple handlers if logger already configured (e.g., in interactive sessions)
    if logger.hasHandlers():
        logger.handlers.clear()
        # Or, if you want to reconfigure with new settings, just proceed.
        # For now, clearing ensures we don't duplicate logs if this function is called multiple times.

    logger.setLevel(log_level)

    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(module)s:%(lineno)d - %(message)s')

    # Create file handler
    try:
        file_handler = logging.FileHandler(LOG_FILE_PATH, encoding='utf-8')
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    except Exception as e:
        print(f"Error setting up file handler for logger: {e}")

    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level) # Or a different level for console, e.g., logging.WARNING
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    return logger

# Initialize a default logger for the application
# Modules can import this logger instance directly
app_logger = setup_logger()

# Example of a specific logger for AI interactions if needed for finer control later
# ai_logger = setup_logger(name='MacInvoicerAI', log_level=logging.DEBUG) 