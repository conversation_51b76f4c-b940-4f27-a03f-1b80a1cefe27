import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src/ai_handler')))
import pytest
from unittest import mock
import openai_ai, dummy_ai

def test_calculate_cost():
    """
    Test calculate_cost returns correct cost for given tokens and model.
    """
    print("Test: calculate_cost should return correct cost.")
    cost = openai_ai.calculate_cost('gpt-4.1-nano', 100, 50)
    assert isinstance(cost, float)
    print(f"Calculated cost: {cost}")

def test_log_responses_api_interaction():
    """
    Test log_responses_api_interaction logs the interaction (mocked).
    """
    print("Test: log_responses_api_interaction should log API interaction (mocked).")
    with mock.patch('macinvoicer.src.ai_handler.openai_ai.app_logger') as mock_logger:
        openai_ai.log_responses_api_interaction('session', {}, {})
        assert mock_logger.setup_logger.called
        print("API interaction logged.")

def test_create_responses_api_request():
    """
    Test create_responses_api_request returns a dict with required keys.
    """
    print("Test: create_responses_api_request should return a dict with required keys.")
    req = openai_ai.create_responses_api_request('text', 'instructions', 'gpt-3.5', 'session', 'file.pdf')
    assert isinstance(req, dict)
    assert 'input' in req
    print(f"API request: {req}")

def test_extract_invoice_data_openai_mocked():
    """
    Test extract_invoice_data_openai returns expected data (mocked API call).
    """
    print("Test: extract_invoice_data_openai should return extracted data (mocked).")
    with mock.patch('macinvoicer.src.ai_handler.openai_ai.call_openai_responses_api', return_value={'invoice': 'data'}):
        result = openai_ai.extract_invoice_data_openai('text', 'file.pdf')
        assert 'invoice' in result
        print(f"Extracted data: {result}")

def test_extract_invoice_data_dummy():
    """
    Test extract_invoice_data_dummy returns dummy data.
    """
    print("Test: extract_invoice_data_dummy should return dummy data.")
    result = dummy_ai.extract_invoice_data_dummy('text', 'file.pdf')
    assert isinstance(result, dict)
    print(f"Dummy AI data: {result}") 