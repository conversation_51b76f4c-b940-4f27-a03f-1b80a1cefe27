# MacInvoicer Installation Guide

This guide provides detailed installation instructions for MacInvoicer across different operating systems and environments.

## Table of Contents

1. [System Requirements](#system-requirements)
2. [Python Environment Setup](#python-environment-setup)
3. [Platform-Specific Dependencies](#platform-specific-dependencies)
4. [Application Installation](#application-installation)
5. [Configuration](#configuration)
6. [Verification](#verification)
7. [Troubleshooting](#troubleshooting)

## System Requirements

### Minimum Requirements
- **Operating System**: Windows 10/11, Ubuntu 18.04+, macOS 10.15+
- **Python**: 3.8 or higher
- **RAM**: 2GB minimum, 4GB recommended
- **Storage**: 500MB for application + space for invoice storage
- **Network**: Internet connection for AI API calls

### Recommended Requirements
- **Python**: 3.10 or higher for optimal performance
- **RAM**: 8GB for processing large batches
- **Storage**: 2GB+ for long-term invoice storage

## Python Environment Setup

### 1. Install Python

#### Windows
1. Download Python from [python.org](https://www.python.org/downloads/windows/)
2. Choose Python 3.10+ installer
3. **Important**: Check "Add Python to PATH" during installation
4. Verify installation:
   ```cmd
   python --version
   pip --version
   ```

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install python3 python3-pip python3-venv
python3 --version
```

#### macOS
```bash
# Using Homebrew (recommended)
brew install python3

# Or download from python.org
# Verify installation
python3 --version
pip3 --version
```

### 2. Create Virtual Environment
Always use a virtual environment to avoid conflicts:

```bash
# Navigate to project directory
cd macinvoicer

# Create virtual environment
python -m venv .venv

# Activate virtual environment
# Windows
.venv\Scripts\activate

# Linux/macOS
source .venv/bin/activate

# Verify activation (prompt should show (.venv))
which python  # Should point to .venv directory
```

## Platform-Specific Dependencies

### Windows Installation

#### 1. Install Tesseract OCR
1. Download Tesseract installer:
   - Visit: https://github.com/UB-Mannheim/tesseract/wiki
   - Download latest version (e.g., tesseract-ocr-w64-setup-v5.3.0.exe)

2. Install Tesseract:
   - Run installer as Administrator
   - Install to default location: `C:\Program Files\Tesseract-OCR\`
   - Note the installation path for configuration

3. Verify installation:
   ```cmd
   "C:\Program Files\Tesseract-OCR\tesseract.exe" --version
   ```

#### 2. Install Poppler
1. Download Poppler for Windows:
   - Visit: https://github.com/oschwartz10612/poppler-windows/releases
   - Download latest release (e.g., Release-23.05.0.zip)

2. Extract and install:
   - Extract to `C:\Program Files\poppler\`
   - The bin directory should be at `C:\Program Files\poppler\bin\`

3. Add to PATH (optional):
   - Open System Properties → Environment Variables
   - Add `C:\Program Files\poppler\bin` to PATH
   - Or configure in `.env` file

4. Verify installation:
   ```cmd
   "C:\Program Files\poppler\bin\pdftoppm.exe" -h
   ```

### Linux Installation (Ubuntu/Debian)

#### Install System Dependencies
```bash
# Update package list
sudo apt update

# Install Tesseract OCR
sudo apt install tesseract-ocr

# Install Poppler utilities
sudo apt install poppler-utils

# Install additional system dependencies
sudo apt install python3-dev build-essential

# Verify installations
tesseract --version
pdftoppm -h
```

#### Additional Language Support (Optional)
```bash
# Install additional Tesseract language packs
sudo apt install tesseract-ocr-deu  # German
sudo apt install tesseract-ocr-fra  # French
sudo apt install tesseract-ocr-spa  # Spanish
```

### macOS Installation

#### Using Homebrew (Recommended)
```bash
# Install Homebrew if not already installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install dependencies
brew install tesseract
brew install poppler

# Verify installations
tesseract --version
pdftoppm -h
```

#### Using MacPorts (Alternative)
```bash
sudo port install tesseract
sudo port install poppler
```

## Application Installation

### 1. Clone or Download the Application
```bash
# Option 1: Clone from repository
git clone <repository-url>
cd macinvoicer

# Option 2: Download and extract ZIP
# Download ZIP file and extract to desired location
cd macinvoicer
```

### 2. Install Python Dependencies
```bash
# Ensure virtual environment is activated
# Install requirements
pip install -r requirements.txt

# Verify installation
pip list
```

### 3. Verify Core Dependencies
```bash
# Test Python imports
python -c "import watchdog, PyPDF2, pandas, openai, pytesseract; print('All imports successful')"
```

## Configuration

### 1. Create Configuration File
```bash
# Copy example configuration
cp config/.env.example config/.env

# Or create manually
touch config/.env
```

### 2. Configure Environment Variables
Edit `config/.env` with your preferred text editor:

```env
# OpenAI Configuration (Required)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4.1-nano

# Windows-specific paths (if not in PATH)
TESSERACT_CMD_PATH=C:\Program Files\Tesseract-OCR\tesseract.exe
POPPLER_PATH=C:\Program Files\poppler\bin

# Linux/macOS (usually not needed if installed via package manager)
# TESSERACT_CMD_PATH=/usr/bin/tesseract
# POPPLER_PATH=/usr/bin
```

### 3. Obtain OpenAI API Key
1. Visit [OpenAI Platform](https://platform.openai.com/)
2. Create account or sign in
3. Navigate to API Keys section
4. Create new API key
5. Copy key to `.env` file
6. **Important**: Keep this key secure and never commit to version control

### 4. Test Configuration
```bash
# Test configuration loading
cd src
python -c "from config_loader import OPENAI_API_KEY, OPENAI_MODEL; print(f'Model: {OPENAI_MODEL}', 'API Key:', 'Configured' if OPENAI_API_KEY else 'Missing')"
```

## Verification

### 1. Test PDF Processing
```bash
cd src
python -c "
from invoice_parser import extract_text_from_pdf
print('PDF processing:', 'Available' if extract_text_from_pdf else 'Error')
"
```

### 2. Test AI Integration
```bash
cd src
python -c "
from ai_handler.openai_ai import client
print('OpenAI client:', 'Connected' if client else 'Not configured')
"
```

### 3. Test Application Startup
```bash
cd src
python main.py
# Should start monitoring (press Ctrl+C to stop)
```

### 4. Directory Structure Verification
After running the application once, verify these directories exist:
```
macinvoicer/
├── monitored_invoices/      ✓ Created
├── output_spreadsheets/     ✓ Created
├── processed_invoices_notes/ ✓ Created
├── logs/                    ✓ Created
└── config/                  ✓ Created
```

## Troubleshooting

### Common Installation Issues

#### Python Import Errors
```bash
# Ensure virtual environment is activated
source .venv/bin/activate  # Linux/macOS
.venv\Scripts\activate     # Windows

# Reinstall dependencies
pip install -r requirements.txt --force-reinstall
```

#### Tesseract Not Found
```bash
# Test Tesseract directly
tesseract --version

# If command not found:
# Windows: Add to PATH or set TESSERACT_CMD_PATH
# Linux: sudo apt install tesseract-ocr
# macOS: brew install tesseract
```

#### Poppler Not Found
```bash
# Test Poppler
pdftoppm -h

# If command not found:
# Windows: Install poppler-windows, set POPPLER_PATH
# Linux: sudo apt install poppler-utils
# macOS: brew install poppler
```

#### Permission Errors
```bash
# Ensure directories are writable
chmod 755 macinvoicer/
chmod 755 macinvoicer/monitored_invoices/
chmod 755 macinvoicer/output_spreadsheets/
```

#### OpenAI API Errors
1. Verify API key is correct
2. Check OpenAI account has credits
3. Test network connectivity
4. Verify model name is supported

### Platform-Specific Issues

#### Windows
- **Long Path Issues**: Enable long path support in Windows 10/11
- **PowerShell Execution Policy**: Set execution policy if needed:
  ```powershell
  Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
  ```

#### Linux
- **Permission Denied**: Use `sudo` for system package installation
- **Missing Development Headers**: Install `python3-dev` package

#### macOS
- **Xcode Command Line Tools**: Install if compilation fails:
  ```bash
  xcode-select --install
  ```

### Getting Help

1. **Check Logs**: Always check `logs/app.log` for detailed error information
2. **Verify Configuration**: Ensure all paths in `.env` are correct
3. **Test Components**: Use the verification steps above to isolate issues
4. **Documentation**: Review other documentation files in `docs/` directory

## Next Steps

After successful installation:
1. Review the [Configuration Guide](CONFIGURATION.md) for advanced options
2. Read the [User Guide](USER_GUIDE.md) for usage instructions
3. Check the [API Reference](API_REFERENCE.md) for development information

---

**Installation complete!** You can now start using MacInvoicer for automated invoice processing. 