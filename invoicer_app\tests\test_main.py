import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))
import pytest
from unittest import mock
import main

def test_process_invoice_calls_ai_and_spreadsheet():
    """
    Integration test: process_invoice should call AI extraction and append to spreadsheet (all mocked).
    """
    print("Test: process_invoice should call AI extraction and append to spreadsheet (mocked).")
    with mock.patch('main.invoice_parser') as mock_parser, \
         mock.patch('main.spreadsheet_manager') as mock_spreadsheet, \
         mock.patch('main.openai_ai') as mock_ai:
        mock_parser.extract_text_from_pdf.return_value = 'PDF text'
        mock_ai.extract_invoice_data_openai.return_value = {'Invoice Number': '123', 'Amount': 100.0}
        main.process_invoice('dummy.pdf')
        assert mock_parser.extract_text_from_pdf.called
        assert mock_ai.extract_invoice_data_openai.called
        assert mock_spreadsheet.append_to_spreadsheet.called
        print("process_invoice called all major components.")

def test_on_created_triggers_processing():
    """
    Integration test: on_created should trigger process_invoice for new PDF files (mocked).
    """
    print("Test: on_created should trigger process_invoice for new PDF files (mocked).")
    handler = main.NewFileHandler()
    with mock.patch('main.process_invoice') as mock_process:
        class Event:
            src_path = 'invoice.pdf'
        handler.on_created(Event())
        assert mock_process.called
        print("on_created triggered process_invoice.") 