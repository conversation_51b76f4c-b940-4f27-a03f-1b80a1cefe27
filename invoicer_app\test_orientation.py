#!/usr/bin/env python3
"""
Test script for PDF orientation detection and correction.

This script tests the PDF orientation detection functionality by:
1. Taking a sample PDF file
2. Detecting its orientation
3. Applying correction if needed
4. Showing before/after results

Usage:
    python test_orientation.py <pdf_file_path>
"""

import sys
import os

# Add src directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.pdf_orientation import detect_pdf_orientation, correct_pdf_orientation
from src.app_logger import app_logger

def test_orientation_detection(pdf_path):
    """Test orientation detection on a PDF file."""
    
    if not os.path.exists(pdf_path):
        print(f"Error: PDF file not found: {pdf_path}")
        return False
    
    print(f"Testing orientation detection on: {os.path.basename(pdf_path)}")
    print("=" * 60)
    
    try:
        # Test orientation detection
        print("1. Detecting PDF orientation...")
        required_rotation = detect_pdf_orientation(pdf_path)
        
        if required_rotation == 0:
            print("✅ PDF is correctly oriented - no rotation needed")
        else:
            print(f"📐 PDF needs rotation: {required_rotation}° clockwise")
        
        # Test orientation correction
        print("\n2. Testing orientation correction...")
        corrected_path = correct_pdf_orientation(pdf_path)
        
        if corrected_path:
            print(f"✅ Orientation corrected successfully")
            print(f"   Original: {pdf_path}")
            print(f"   Corrected: {corrected_path}")
            
            # Check file sizes
            original_size = os.path.getsize(pdf_path)
            corrected_size = os.path.getsize(corrected_path)
            print(f"   Original size: {original_size:,} bytes")
            print(f"   Corrected size: {corrected_size:,} bytes")
            
            # Clean up
            try:
                os.unlink(corrected_path)
                print("   ✅ Temporary corrected file cleaned up")
            except Exception as e:
                print(f"   ⚠️  Could not clean up temporary file: {e}")
                
        else:
            if required_rotation == 0:
                print("✅ No correction needed - PDF is already correctly oriented")
            else:
                print("❌ Orientation correction failed")
        
        print("\n" + "=" * 60)
        print("Test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    if len(sys.argv) != 2:
        print("Usage: python test_orientation.py <pdf_file_path>")
        print("\nExample:")
        print("  python test_orientation.py monitored_invoices/sample_invoice.pdf")
        return 1
    
    pdf_path = sys.argv[1]
    
    # Make path absolute if relative
    if not os.path.isabs(pdf_path):
        pdf_path = os.path.abspath(pdf_path)
    
    success = test_orientation_detection(pdf_path)
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
