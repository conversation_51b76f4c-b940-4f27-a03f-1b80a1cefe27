#!/usr/bin/env python3
"""
Simple test for OSD (Orientation and Script Detection) functionality.
"""

import sys
import os

# Add src directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from pdf2image import convert_from_path
from src.pdf_orientation import detect_text_orientation_osd
from src.config_loader import POPPLER_PATH

def test_osd_simple(pdf_path):
    """Simple OSD test."""
    
    print(f"Testing OSD on: {os.path.basename(pdf_path)}")
    print("=" * 50)
    
    try:
        # Convert first page to image
        print("Converting PDF to image...")
        images = convert_from_path(
            pdf_path, 
            first_page=1, 
            last_page=1,
            poppler_path=POPPLER_PATH,
            dpi=200
        )
        
        if not images:
            print("❌ Could not convert PDF to image")
            return False
        
        image = images[0]
        print(f"✅ Converted to image: {image.size}")
        
        # Test OSD
        print("Running OSD...")
        osd_result = detect_text_orientation_osd(image)
        
        print(f"OSD Results:")
        print(f"  Success: {osd_result['success']}")
        print(f"  Orientation: {osd_result['orientation']}°")
        print(f"  Rotate needed: {osd_result['rotate']}°")
        print(f"  Script: {osd_result['script']}")
        print(f"  Confidence: {osd_result['confidence']:.1f}")
        
        if osd_result['success'] and osd_result['rotate'] != 0:
            print(f"✅ Orientation correction needed: {osd_result['rotate']}°")
        elif osd_result['success']:
            print("✅ PDF is correctly oriented")
        else:
            print("❌ OSD failed")
        
        return osd_result['success']
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    if len(sys.argv) != 2:
        print("Usage: python test_osd_simple.py <pdf_file_path>")
        return 1
    
    pdf_path = sys.argv[1]
    
    # Make path absolute if relative
    if not os.path.isabs(pdf_path):
        pdf_path = os.path.abspath(pdf_path)
    
    if not os.path.exists(pdf_path):
        print(f"Error: PDF file not found: {pdf_path}")
        return 1
    
    success = test_osd_simple(pdf_path)
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
