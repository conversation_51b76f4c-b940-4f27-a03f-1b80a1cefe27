"""
Document conversion module for the MacInvoicer application.

This module handles conversion of Word and Excel documents to text and PDF formats
for processing by the invoice extraction pipeline. It supports both text-based
and hybrid AI processing workflows.

Key Features:
    - Word document (.docx, .doc) text extraction
    - Excel document (.xlsx, .xls) text extraction  
    - PDF conversion for hybrid processing
    - Comprehensive error handling and logging
    - Temporary file management

Dependencies:
    - python-docx: For Word document processing
    - openpyxl: For Excel document processing
    - pandas: For Excel data handling
    - reportlab: For PDF generation
    - xlrd: For legacy Excel format support

Author: MacInvoicer Development Team
Version: 1.0.0
"""

import os
import tempfile
from app_logger import app_logger

# Import libraries with error handling for missing dependencies
try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    app_logger.warning("python-docx not available. Word document support will be limited.")

try:
    import openpyxl
    import pandas as pd
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    app_logger.warning("openpyxl/pandas not available. Excel document support will be limited.")

try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter
    from reportlab.lib.utils import simpleSplit
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    app_logger.warning("reportlab not available. PDF conversion for hybrid processing will be limited.")

try:
    import xlrd
    XLRD_AVAILABLE = True
except ImportError:
    XLRD_AVAILABLE = False
    app_logger.info("xlrd not available. Legacy .xls file support will be limited.")

def convert_docx_to_text(file_path):
    """
    Extract text content from a Word document (.docx format).
    
    This function reads a .docx file and extracts all text content including
    paragraphs and table data, formatting it for AI processing.
    
    Args:
        file_path (str): Absolute path to the .docx file.
        
    Returns:
        str or None: Extracted text content, or None if extraction fails.
        
    Note:
        Requires python-docx library. Tables are converted to pipe-separated format.
    """
    if not DOCX_AVAILABLE:
        app_logger.error("python-docx not available. Cannot process .docx files.")
        return None
        
    try:
        app_logger.info(f"Extracting text from Word document: {os.path.basename(file_path)}")
        doc = Document(file_path)
        text_content = []
        
        # Extract paragraph text
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():  # Skip empty paragraphs
                text_content.append(paragraph.text.strip())
        
        # Extract table content
        for table in doc.tables:
            table_text = []
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    cell_text = cell.text.strip().replace('\n', ' ')
                    row_text.append(cell_text)
                if any(row_text):  # Skip empty rows
                    table_text.append(" | ".join(row_text))
            
            if table_text:
                text_content.append("\n--- Table ---")
                text_content.extend(table_text)
                text_content.append("--- End Table ---\n")
        
        result = "\n".join(text_content)
        app_logger.info(f"Successfully extracted {len(result)} characters from Word document")
        return result
        
    except Exception as e:
        app_logger.error(f"Error extracting text from Word document {file_path}: {e}", exc_info=True)
        return None

def convert_xlsx_to_text(file_path):
    """
    Extract text content from an Excel document (.xlsx format).
    
    This function reads an .xlsx file and extracts all data from all sheets,
    formatting it as structured text for AI processing.
    
    Args:
        file_path (str): Absolute path to the .xlsx file.
        
    Returns:
        str or None: Extracted text content, or None if extraction fails.
        
    Note:
        Requires openpyxl and pandas libraries. All sheets are processed.
    """
    if not EXCEL_AVAILABLE:
        app_logger.error("openpyxl/pandas not available. Cannot process .xlsx files.")
        return None
        
    try:
        app_logger.info(f"Extracting text from Excel document: {os.path.basename(file_path)}")
        
        # Read all sheets
        excel_data = pd.read_excel(file_path, sheet_name=None, engine='openpyxl')
        text_content = []
        
        for sheet_name, df in excel_data.items():
            if df.empty:
                continue
                
            text_content.append(f"\n=== Sheet: {sheet_name} ===")
            
            # Convert DataFrame to string with proper formatting
            df_string = df.to_string(index=False, na_rep='')
            text_content.append(df_string)
            text_content.append(f"=== End Sheet: {sheet_name} ===\n")
        
        result = "\n".join(text_content)
        app_logger.info(f"Successfully extracted {len(result)} characters from Excel document")
        return result
        
    except Exception as e:
        app_logger.error(f"Error extracting text from Excel document {file_path}: {e}", exc_info=True)
        return None

def convert_xls_to_text(file_path):
    """
    Extract text content from a legacy Excel document (.xls format).
    
    This function reads an .xls file and extracts all data from all sheets,
    formatting it as structured text for AI processing.
    
    Args:
        file_path (str): Absolute path to the .xls file.
        
    Returns:
        str or None: Extracted text content, or None if extraction fails.
        
    Note:
        Requires xlrd library for legacy Excel format support.
    """
    try:
        app_logger.info(f"Extracting text from legacy Excel document: {os.path.basename(file_path)}")
        
        # Try pandas first (works with newer xlrd versions)
        try:
            excel_data = pd.read_excel(file_path, sheet_name=None, engine='xlrd')
            text_content = []
            
            for sheet_name, df in excel_data.items():
                if df.empty:
                    continue
                    
                text_content.append(f"\n=== Sheet: {sheet_name} ===")
                df_string = df.to_string(index=False, na_rep='')
                text_content.append(df_string)
                text_content.append(f"=== End Sheet: {sheet_name} ===\n")
            
            result = "\n".join(text_content)
            app_logger.info(f"Successfully extracted {len(result)} characters from legacy Excel document")
            return result
            
        except Exception as pandas_error:
            app_logger.warning(f"Pandas extraction failed for .xls file, trying direct xlrd: {pandas_error}")
            
            # Fallback to direct xlrd usage if available
            if not XLRD_AVAILABLE:
                app_logger.error("xlrd not available. Cannot process .xls files.")
                return None
                
            import xlrd
            workbook = xlrd.open_workbook(file_path)
            text_content = []
            
            for sheet_name in workbook.sheet_names():
                sheet = workbook.sheet_by_name(sheet_name)
                text_content.append(f"\n=== Sheet: {sheet_name} ===")
                
                for row_idx in range(sheet.nrows):
                    row_values = []
                    for col_idx in range(sheet.ncols):
                        cell_value = sheet.cell_value(row_idx, col_idx)
                        row_values.append(str(cell_value))
                    text_content.append(" | ".join(row_values))
                
                text_content.append(f"=== End Sheet: {sheet_name} ===\n")
            
            result = "\n".join(text_content)
            app_logger.info(f"Successfully extracted {len(result)} characters from legacy Excel document using xlrd")
            return result
            
    except Exception as e:
        app_logger.error(f"Error extracting text from legacy Excel document {file_path}: {e}", exc_info=True)
        return None

def convert_doc_to_text(file_path):
    """
    Extract text content from a legacy Word document (.doc format).

    This function attempts to extract text from .doc files. Note that .doc
    format support is limited and may require additional tools.

    Args:
        file_path (str): Absolute path to the .doc file.

    Returns:
        str or None: Extracted text content, or None if extraction fails.

    Note:
        .doc format support is limited. Consider converting to .docx first.
    """
    app_logger.warning(f"Legacy .doc format detected: {os.path.basename(file_path)}")
    app_logger.warning("Direct .doc support is limited. Consider converting to .docx format.")

    # For now, return None to indicate unsupported format
    # Future enhancement could add python-docx2txt or LibreOffice conversion
    return None

def get_document_text(file_path):
    """
    Extract text from a document file based on its extension.

    This is the main entry point for document text extraction. It determines
    the file type and routes to the appropriate conversion function.

    Args:
        file_path (str): Absolute path to the document file.

    Returns:
        str or None: Extracted text content, or None if extraction fails.

    Supported formats:
        - .docx: Word documents
        - .doc: Legacy Word documents (limited support)
        - .xlsx: Excel spreadsheets
        - .xls: Legacy Excel spreadsheets
    """
    if not os.path.exists(file_path):
        app_logger.error(f"Document file not found: {file_path}")
        return None

    file_extension = os.path.splitext(file_path)[1].lower()
    filename = os.path.basename(file_path)

    app_logger.info(f"Processing document: {filename} (type: {file_extension})")

    if file_extension == '.docx':
        return convert_docx_to_text(file_path)
    elif file_extension == '.doc':
        return convert_doc_to_text(file_path)
    elif file_extension == '.xlsx':
        return convert_xlsx_to_text(file_path)
    elif file_extension == '.xls':
        return convert_xls_to_text(file_path)
    else:
        app_logger.error(f"Unsupported document format: {file_extension}")
        return None

def convert_document_to_pdf_for_hybrid(file_path):
    """
    Convert a Word or Excel document to PDF for hybrid AI processing.

    This function creates a temporary PDF file from the document content
    for use with OpenAI's file upload API in hybrid processing mode.

    Args:
        file_path (str): Absolute path to the document file.

    Returns:
        str or None: Path to temporary PDF file, or None if conversion fails.

    Note:
        The caller is responsible for cleaning up the temporary PDF file.
        Requires reportlab library for PDF generation.
    """
    if not REPORTLAB_AVAILABLE:
        app_logger.error("reportlab not available. Cannot create PDF for hybrid processing.")
        return None

    try:
        # Extract text from document
        text_content = get_document_text(file_path)
        if not text_content:
            app_logger.error(f"Could not extract text from document for PDF conversion: {file_path}")
            return None

        # Create temporary PDF file
        temp_pdf = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_pdf.close()

        app_logger.info(f"Creating temporary PDF for hybrid processing: {temp_pdf.name}")

        # Create PDF with extracted text
        c = canvas.Canvas(temp_pdf.name, pagesize=letter)
        width, height = letter
        margin = 50
        line_height = 15
        max_width = width - 2 * margin

        # Split text into lines that fit the page width
        lines = []
        for paragraph in text_content.split('\n'):
            if not paragraph.strip():
                lines.append('')  # Preserve empty lines
                continue

            # Split long lines to fit page width
            wrapped_lines = simpleSplit(paragraph, 'Helvetica', 10, max_width)
            lines.extend(wrapped_lines)

        # Write text to PDF
        y = height - margin
        for line in lines:
            if y < margin:  # Start new page
                c.showPage()
                y = height - margin

            # Encode text to handle special characters
            try:
                safe_line = line.encode('latin-1', 'replace').decode('latin-1')
            except:
                safe_line = line.encode('ascii', 'replace').decode('ascii')

            c.drawString(margin, y, safe_line)
            y -= line_height

        c.save()

        file_size = os.path.getsize(temp_pdf.name)
        app_logger.info(f"Successfully created temporary PDF: {temp_pdf.name} ({file_size} bytes)")

        return temp_pdf.name

    except Exception as e:
        app_logger.error(f"Error converting document to PDF for hybrid processing: {e}", exc_info=True)
        return None

def cleanup_temp_file(file_path):
    """
    Clean up a temporary file created during document processing.

    Args:
        file_path (str): Path to the temporary file to remove.

    Returns:
        bool: True if cleanup was successful, False otherwise.
    """
    if not file_path or not os.path.exists(file_path):
        return True

    try:
        os.remove(file_path)
        app_logger.debug(f"Cleaned up temporary file: {file_path}")
        return True
    except Exception as e:
        app_logger.error(f"Error cleaning up temporary file {file_path}: {e}")
        return False
