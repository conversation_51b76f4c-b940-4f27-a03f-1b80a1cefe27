import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))
import shutil
import tempfile
import pytest
import config_loader

def test_ensure_directories_exist_creates_directories():
    """
    Test that ensure_directories_exist creates the required directories if they do not exist.
    This test uses a temporary directory to avoid affecting the real filesystem.
    """
    print("Test: ensure_directories_exist should create required directories if missing.")
    temp_dir = tempfile.mkdtemp()
    original_dirs = config_loader.REQUIRED_DIRS.copy()
    try:
        # Patch REQUIRED_DIRS to use temp_dir
        config_loader.REQUIRED_DIRS = [os.path.join(temp_dir, d) for d in ["a", "b"]]
        # Remove if exists
        for d in config_loader.REQUIRED_DIRS:
            if os.path.exists(d):
                shutil.rmtree(d)
        config_loader.ensure_directories_exist()
        for d in config_loader.REQUIRED_DIRS:
            assert os.path.exists(d)
            print(f"Directory created: {d}")
    finally:
        config_loader.REQUIRED_DIRS = original_dirs
        shutil.rmtree(temp_dir)

def test_ensure_directories_exist_handles_existing():
    """
    Test that ensure_directories_exist does not fail if directories already exist.
    """
    print("Test: ensure_directories_exist should not fail if directories already exist.")
    temp_dir = tempfile.mkdtemp()
    original_dirs = config_loader.REQUIRED_DIRS.copy()
    try:
        config_loader.REQUIRED_DIRS = [os.path.join(temp_dir, d) for d in ["a", "b"]]
        for d in config_loader.REQUIRED_DIRS:
            os.makedirs(d, exist_ok=True)
        config_loader.ensure_directories_exist()  # Should not raise
        print("No error when directories already exist.")
    finally:
        config_loader.REQUIRED_DIRS = original_dirs
        shutil.rmtree(temp_dir) 