# Directory Tree for invoicer_app

Generated on: 2025-05-31 21:06:47

```
├── invoicer_app/
│   ├── config/
│   ├── docs/
│   │   ├── CONFIGURATION.md (11.9KB)
│   │   ├── INSTALLATION.md (9.2KB)
│   │   └── USER_GUIDE.md (11.1KB)
│   ├── logs/
│   │   └── app.log (328.5KB)
│   ├── monitored_invoices/
│   │   ├── Invoice_001098.pdf (615.5KB)
│   │   ├── Invoice_1383_from_Fierce_Mild_Brewing_Limited.pdf (55.5KB)
│   │   ├── Invoice_24378_from_Cerberus_Security_Ltd.pdf (142.1KB)
│   │   ├── Invoice_SI-00016585.pdf (67.9KB)
│   │   ├── JCP_Sales_Invoice_v2JCP015.1_-_SI-060633.pdf (16.0KB)
│   │   ├── SC332-1.pdf (46.4KB)
│   │   ├── SC332-22.pdf (55.4KB)
│   │   └── SC332-3.pdf (39.4KB)
│   ├── output_spreadsheets/
│   │   ├── 2025-05_invoices.xlsx (7.1KB)
│   │   └── approved_invoices_20250531_192411.xlsx (4.7KB)
│   ├── processed_invoices_notes/
│   │   ├── Invoice_001098_processing_note.txt (1.9KB)
│   │   ├── Invoice_1383_from_Fierce_Mild_Brewing_Limited_processing_note.txt (2.0KB)
│   │   ├── Invoice_24378_from_Cerberus_Security_Ltd_processing_note.txt (2.0KB)
│   │   ├── Invoice_SI-00016585_processing_note.txt (1.9KB)
│   │   ├── JCP_Sales_Invoice_v2JCP015.1_-_SI-060633_processing_note.txt (2.0KB)
│   │   ├── SC332-1_processing_note.txt (1.4KB)
│   │   ├── SC332-22_processing_note.txt (2.0KB)
│   │   └── SC332-3_processing_note.txt (1.4KB)
│   ├── src/
│   │   ├── __pycache__/
│   │   │   ├── app_logger.cpython-310.pyc (1.2KB)
│   │   │   ├── app_logger.cpython-312.pyc (3.7KB)
│   │   │   ├── app_logger.cpython-313.pyc (3.6KB)
│   │   │   ├── config_loader.cpython-310.pyc (1.6KB)
│   │   │   ├── config_loader.cpython-312.pyc (4.1KB)
│   │   │   ├── config_loader.cpython-313.pyc (4.0KB)
│   │   │   ├── invoice_parser.cpython-310.pyc (3.2KB)
│   │   │   ├── invoice_parser.cpython-312.pyc (12.5KB)
│   │   │   ├── invoice_parser.cpython-313.pyc (12.6KB)
│   │   │   ├── main.cpython-310.pyc (4.3KB)
│   │   │   ├── main.cpython-312.pyc (6.9KB)
│   │   │   ├── main.cpython-313.pyc (11.0KB)
│   │   │   ├── spreadsheet_manager.cpython-312.pyc (13.2KB)
│   │   │   └── spreadsheet_manager.cpython-313.pyc (13.1KB)
│   │   ├── ai_handler/
│   │   │   ├── __pycache__/
│   │   │   │   ├── __init__.cpython-312.pyc (178B)
│   │   │   │   ├── __init__.cpython-313.pyc (178B)
│   │   │   │   ├── dummy_ai.cpython-312.pyc (3.2KB)
│   │   │   │   ├── dummy_ai.cpython-313.pyc (3.1KB)
│   │   │   │   ├── openai_ai.cpython-310.pyc (9.5KB)
│   │   │   │   ├── openai_ai.cpython-312.pyc (50.1KB)
│   │   │   │   └── openai_ai.cpython-313.pyc (49.9KB)
│   │   │   ├── __init__.py (29B)
│   │   │   ├── dummy_ai.py (2.9KB)
│   │   │   └── openai_ai.py (54.0KB)
│   │   ├── app_logger.py (3.8KB)
│   │   ├── config_loader.py (3.7KB)
│   │   ├── invoice_parser.py (11.8KB)
│   │   ├── main.py (10.2KB)
│   │   ├── spreadsheet_manager.py (14.8KB)
│   │   └── test_hybrid.py (1.2KB)
│   ├── tests/
│   │   ├── __pycache__/
│   │   │   ├── test_ai_handler.cpython-310-pytest-8.3.2.pyc (4.2KB)
│   │   │   ├── test_ai_handler.cpython-312-pytest-8.3.5.pyc (7.7KB)
│   │   │   ├── test_app_logger.cpython-310-pytest-8.3.2.pyc (2.2KB)
│   │   │   ├── test_app_logger.cpython-312-pytest-8.3.5.pyc (3.6KB)
│   │   │   ├── test_config_loader.cpython-310-pytest-8.3.2.pyc (2.5KB)
│   │   │   ├── test_config_loader.cpython-312-pytest-8.3.5.pyc (4.3KB)
│   │   │   ├── test_invoice_parser.cpython-310-pytest-8.3.2.pyc (2.1KB)
│   │   │   ├── test_invoice_parser.cpython-312-pytest-8.3.5.pyc (3.4KB)
│   │   │   ├── test_main.cpython-310-pytest-8.3.2.pyc (3.0KB)
│   │   │   ├── test_main.cpython-312-pytest-8.3.5.pyc (5.2KB)
│   │   │   ├── test_spreadsheet_manager.cpython-310-pytest-8.3.2.pyc (2.8KB)
│   │   │   └── test_spreadsheet_manager.cpython-312-pytest-8.3.5.pyc (5.0KB)
│   │   ├── README.md (958B)
│   │   ├── test_ai_handler.py (2.2KB)
│   │   ├── test_app_logger.py (1.1KB)
│   │   ├── test_config_loader.py (1.9KB)
│   │   ├── test_invoice_parser.py (1.1KB)
│   │   ├── test_main.py (1.6KB)
│   │   └── test_spreadsheet_manager.py (1.3KB)
│   ├── ui/
│   │   ├── __pycache__/
│   │   │   ├── app.cpython-312.pyc (20.3KB)
│   │   │   └── import_data.cpython-312.pyc (6.7KB)
│   │   ├── data/
│   │   │   └── invoices.db (20.0KB)
│   │   ├── static/
│   │   │   ├── pdf.js/
│   │   │   │   ├── pdf.min.js (280.2KB)
│   │   │   │   └── pdf.worker.min.js (1.0MB)
│   │   │   ├── app.js (5.6KB)
│   │   │   └── style.css (3.5KB)
│   │   ├── templates/
│   │   │   ├── index.html (3.5KB)
│   │   │   ├── review.html (4.5KB)
│   │   │   └── upload.html (18.0KB)
│   │   ├── app.py (15.1KB)
│   │   ├── import_data.py (6.2KB)
│   │   ├── README.md (2.6KB)
│   │   └── setup_db.py (999B)
│   ├── API_FIX_SUMMARY.md (4.0KB)
│   ├── CONFIGURATION.md (1B)
│   ├── ENHANCEMENT_SUMMARY.md (6.9KB)
│   ├── HYBRID_PROCESSING_ENHANCEMENT.md (6.5KB)
│   ├── invoice_processing_tool_plan.md (10.3KB)
│   ├── README.md (10.8KB)
│   └── requirements.txt (189B)
```

---
*This file was automatically generated by tree_to_md.py*
