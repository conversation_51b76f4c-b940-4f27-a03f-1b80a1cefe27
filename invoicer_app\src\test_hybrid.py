#!/usr/bin/env python3
"""
Simple test script to test the hybrid extraction functionality.
"""

import os
import sys
from ai_handler.openai_ai import extract_invoice_data_hybrid

def test_hybrid_extraction():
    """Test the hybrid extraction with a dummy PDF path."""
    print("Testing hybrid extraction...")
    
    # Create a dummy PDF path (doesn't need to exist for this test)
    test_pdf_path = "test_invoice.pdf"
    test_filename = "test_invoice.pdf"
    
    try:
        result = extract_invoice_data_hybrid(test_pdf_path, test_filename)
        print(f"Hybrid extraction completed successfully!")
        print(f"Result type: {type(result)}")
        print(f"Result keys: {list(result.keys()) if result else 'None'}")
        print(f"Invoice ID: {result.get('invoice_id') if result else 'None'}")
        return True
    except Exception as e:
        print(f"Error in hybrid extraction: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_hybrid_extraction()
    if success:
        print("Test completed successfully!")
    else:
        print("Test failed!")
        sys.exit(1) 