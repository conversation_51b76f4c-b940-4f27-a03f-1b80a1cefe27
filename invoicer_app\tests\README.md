# Test Suite for MacInvoicer

## How to Run the Tests

1. Install test dependencies (pytest, pandas, etc.) if not already installed:
   ```
   pip install -r requirements.txt
   pip install pytest pandas openpyxl
   ```
2. Run all tests from the project root:
   ```
   pytest macinvoicer/tests
   ```

## Test File Overview

- `test_config_loader.py`: Tests directory creation and config loading logic.
- `test_app_logger.py`: Tests logger setup and log file creation.
- `test_spreadsheet_manager.py`: Tests spreadsheet path generation and data appending.
- `test_invoice_parser.py`: Tests OCR and PDF text extraction (with mocks).
- `test_ai_handler.py`: Tests AI handler logic, API request formatting, and dummy AI fallback (with mocks).
- `test_main.py`: Integration tests for invoice processing and file event handling (with mocks).

Each test function includes a docstring and print statements to explain its purpose and output. 