Step 1: Initial Analysis
Load every .md or .py file supplied, beginning with • invoice_parser.md (or equivalent) • openai_integration.py (shown above) • Any model / DTO definitions (models.py, schemas.py, etc.)
Search for hard-coded or implicit assumptions that: a. Only one VAT rate exists (vat_rate scalar used globally). b. Line totals are always positive (no handling of returns). c. Discounts are ignored or subtracted only at grand-total level.
For each occurrence record: • File + line number • Code fragment (≤ 3 lines) • Why it breaks under the new requirements.
Summarise all break-points in a table headed: | # | Location | Old Assumption | Failure With Multi-VAT / Discounts / Returns |
Do NOT propose fixes yet—only list the fragile spots.

Step 2: Reasoning & Redesign
For every break-point identified:

Explain, in plain language, the correct behaviour when: • Two or more VAT rates appear (e.g. 23 % & 9 %). • A percentage or fixed-amount discount applies to a single line. • A whole-invoice discount (early-payment, promotion, etc.) appears. • A returned item (negative quantity or credit memo line) appears.
Derive formal rules: • VAT must be calculated per line BEFORE any invoice-level discount. • Discounts reduce the VAT base; returned items invert quantity, net amount and VAT. • Final “VAT summary” table must list one entry per distinct rate with: {rate, net, vat_amount}.
List edge cases explicitly: a. 0 % (“exempt”) lines alongside taxable lines. b. Multiple discounts on one line (rare but possible). c. Refund lines that reference a previous invoice (credit notes). d. Rounding differences between supplier and our calculation (0.01 tolerance).
No code yet—pure analysis.

Step 3: Data-Structure & Algorithm Design
Propose a new data model. Minimum JSON / Pydantic style:
Invoice {
  header: { id, vendor, date, currency, ... }
  lines: [
    { description, quantity, unit_price, net, vat_rate, vat_amount,
      discount_pct?, discount_abs?, is_return? }
  ],
  invoice_level_discount?: { pct?, abs? },
  vat_breakdown: [
    { vat_rate, net, vat_amount }
  ],
  totals: { net, vat, gross }
}
Explain why each new field is needed and how it is populated.
Show how the existing “single vat_rate, vat_amount, total_amount” fields can be deprecated or computed from the new structures for backward compatibility.
Step 4: Extraction & Processing Algorithms (Natural Language)
A. Extraction Phase

Detect line items: regex + positional heuristics.
Extract raw tokens for “rate”, “net”, “vat”, “discount”, “CR/return”.
Classify each line: normal, discount, return, summary.
Build lines[] with preliminary parsing.
B. Processing Phase

For each line: • If is_return == true, multiply quantity, net, vat_amount by –1. • If discount_pct/abs present, reduce net before VAT calc. • Calculate vat_amount_line = net_after_discount * vat_rate / 100.
Aggregate by vat_rate into vat_breakdown[].
Apply invoice-level discount (if any) proportionally across rates.
Sum to totals.
Validate against supplier’s summary; flag differences > 0.02 currency units.
C. Output Phase
• Return structured JSON; keep legacy flat fields for compatibility (total_amount_eur, etc.)

Step 5: Implementation Plan
Create/extend models (InvoiceLine, InvoiceTotals, VatSummary).
Refactor extraction pipeline: a. Replace scalar vat_rate with per-line field. b. Add regex library for discount detection ((?:disc|rebate|promo) …). c. Recognise negative quantities / “CR” / “credit” keywords for returns.
Update calculation utilities: • New compute_vat_breakdown(lines) function. • New apply_invoice_level_discount(invoice, discount_obj).
Adjust API schema and downstream consumers (DB, UI) to accept new fields.
Unit tests: • Provide at least 5 new test invoices: – Only 23 % VAT (control) – 23 % + 9 % VAT – Per-line discount – Whole-invoice discount – Returned items • Assert breakdown totals == supplier totals within 0.02.
Migration strategy: keep writing both old and new JSON until all consumers migrate.
Documentation & changelog.