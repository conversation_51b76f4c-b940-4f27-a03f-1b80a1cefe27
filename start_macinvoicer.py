#!/usr/bin/env python3
"""
MacInvoicer Startup Script
This script starts both the main invoice processing service and the web UI.
"""

import os
import sys
import subprocess
import time
import signal
from pathlib import Path

def print_colored(message, color="white"):
    """Print colored messages to console"""
    colors = {
        "red": "\033[91m",
        "green": "\033[92m", 
        "yellow": "\033[93m",
        "blue": "\033[94m",
        "cyan": "\033[96m",
        "white": "\033[97m",
        "reset": "\033[0m"
    }
    print(f"{colors.get(color, colors['white'])}{message}{colors['reset']}")

def main():
    print_colored("=== MacInvoicer Startup Script ===", "green")
    print_colored("Starting MacInvoicer application...", "yellow")
    
    # Get script directory
    script_dir = Path(__file__).parent.absolute()
    print_colored(f"Script directory: {script_dir}", "cyan")
    
    # Define paths
    venv_path = script_dir / ".venv"
    if os.name == 'nt':  # Windows
        python_exe = venv_path / "Scripts" / "python.exe"
        activate_script = venv_path / "Scripts" / "activate.bat"
    else:  # Unix/Linux/Mac
        python_exe = venv_path / "bin" / "python"
        activate_script = venv_path / "bin" / "activate"
    
    main_script = script_dir / "invoicer_app" / "src" / "main.py"
    app_script = script_dir / "invoicer_app" / "ui" / "app.py"
    
    # Check if virtual environment exists
    if not venv_path.exists():
        print_colored(f"ERROR: Virtual environment not found at: {venv_path}", "red")
        print_colored("Please create a virtual environment first by running:", "yellow")
        print_colored("python -m venv .venv", "white")
        print_colored("Then install requirements:", "yellow")
        if os.name == 'nt':
            print_colored(".venv\\Scripts\\activate && pip install -r invoicer_app\\requirements.txt", "white")
        else:
            print_colored("source .venv/bin/activate && pip install -r invoicer_app/requirements.txt", "white")
        return 1
    
    # Check if scripts exist
    if not main_script.exists():
        print_colored(f"ERROR: main.py not found at: {main_script}", "red")
        return 1
    
    if not app_script.exists():
        print_colored(f"ERROR: app.py not found at: {app_script}", "red")
        return 1
    
    print_colored(f"Virtual environment found: {venv_path}", "green")
    print_colored(f"Python executable: {python_exe}", "green")
    
    # Store process references
    main_process = None
    app_process = None
    
    def cleanup_processes():
        """Clean up child processes"""
        if main_process and main_process.poll() is None:
            print_colored("Stopping main process...", "yellow")
            main_process.terminate()
            try:
                main_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                main_process.kill()
        
        if app_process and app_process.poll() is None:
            print_colored("Stopping app process...", "yellow")
            app_process.terminate()
            try:
                app_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                app_process.kill()
    
    def signal_handler(signum, frame):
        """Handle Ctrl+C gracefully"""
        print_colored("\nReceived interrupt signal. Shutting down...", "yellow")
        cleanup_processes()
        sys.exit(0)
    
    # Set up signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    if hasattr(signal, 'SIGTERM'):
        signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Start main.py process
        print_colored("Starting main.py (invoice processing service)...", "yellow")
        main_process = subprocess.Popen(
            [str(python_exe), str(main_script)],
            cwd=str(script_dir),
            creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
        )
        print_colored("Main.py started in background", "green")
        
        # Wait for main.py to initialize
        print_colored("Waiting 3 seconds for main.py to initialize...", "yellow")
        time.sleep(3)
        
        # Start app.py process
        print_colored("Starting app.py (web UI)...", "yellow")
        print_colored("Web UI will be available at: http://127.0.0.1:5000", "cyan")
        print_colored("Press Ctrl+C to stop both services", "yellow")
        print()
        
        app_process = subprocess.Popen(
            [str(python_exe), str(app_script)],
            cwd=str(script_dir)
        )
        
        # Wait for app process to complete
        app_process.wait()
        
    except KeyboardInterrupt:
        print_colored("\nKeyboard interrupt received", "yellow")
    except Exception as e:
        print_colored(f"ERROR: An error occurred during startup: {e}", "red")
        return 1
    finally:
        cleanup_processes()
    
    print_colored("MacInvoicer has stopped.", "yellow")
    return 0

if __name__ == "__main__":
    sys.exit(main())
