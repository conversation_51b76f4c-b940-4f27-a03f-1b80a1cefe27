"""
Unit tests for the document_converter module.

This module tests the document conversion functionality for Word and Excel files,
including text extraction and PDF conversion for hybrid processing.

Author: MacInvoicer Development Team
Version: 1.0.0
"""

import sys
import os
import tempfile
import unittest
from unittest import mock

# Add the src directory to the path so we can import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))

# Import the module to test
import document_converter

class TestDocumentConverter(unittest.TestCase):
    """Test cases for document converter functionality."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.test_files_dir = os.path.join(os.path.dirname(__file__), 'test_files')
        os.makedirs(self.test_files_dir, exist_ok=True)
    
    def tearDown(self):
        """Clean up after each test method."""
        # Clean up any test files if needed
        pass
    
    def test_get_document_text_nonexistent_file(self):
        """Test get_document_text with a non-existent file."""
        result = document_converter.get_document_text('/nonexistent/file.docx')
        self.assertIsNone(result)
    
    def test_get_document_text_unsupported_format(self):
        """Test get_document_text with an unsupported file format."""
        # Create a temporary file with unsupported extension
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as temp_file:
            temp_file.write(b'Test content')
            temp_file_path = temp_file.name
        
        try:
            result = document_converter.get_document_text(temp_file_path)
            self.assertIsNone(result)
        finally:
            os.unlink(temp_file_path)
    
    @mock.patch('document_converter.DOCX_AVAILABLE', False)
    def test_convert_docx_to_text_library_unavailable(self):
        """Test convert_docx_to_text when python-docx is not available."""
        result = document_converter.convert_docx_to_text('test.docx')
        self.assertIsNone(result)
    
    @mock.patch('document_converter.EXCEL_AVAILABLE', False)
    def test_convert_xlsx_to_text_library_unavailable(self):
        """Test convert_xlsx_to_text when openpyxl/pandas is not available."""
        result = document_converter.convert_xlsx_to_text('test.xlsx')
        self.assertIsNone(result)
    
    @mock.patch('document_converter.REPORTLAB_AVAILABLE', False)
    def test_convert_document_to_pdf_for_hybrid_library_unavailable(self):
        """Test convert_document_to_pdf_for_hybrid when reportlab is not available."""
        result = document_converter.convert_document_to_pdf_for_hybrid('test.docx')
        self.assertIsNone(result)
    
    def test_convert_doc_to_text_unsupported(self):
        """Test convert_doc_to_text returns None for unsupported .doc format."""
        result = document_converter.convert_doc_to_text('test.doc')
        self.assertIsNone(result)
    
    def test_cleanup_temp_file_nonexistent(self):
        """Test cleanup_temp_file with non-existent file."""
        result = document_converter.cleanup_temp_file('/nonexistent/file.pdf')
        self.assertTrue(result)
    
    def test_cleanup_temp_file_none_path(self):
        """Test cleanup_temp_file with None path."""
        result = document_converter.cleanup_temp_file(None)
        self.assertTrue(result)
    
    def test_cleanup_temp_file_success(self):
        """Test successful cleanup of temporary file."""
        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(b'Test content')
            temp_file_path = temp_file.name
        
        # Verify file exists
        self.assertTrue(os.path.exists(temp_file_path))
        
        # Clean up the file
        result = document_converter.cleanup_temp_file(temp_file_path)
        self.assertTrue(result)
        
        # Verify file is deleted
        self.assertFalse(os.path.exists(temp_file_path))
    
    @mock.patch('document_converter.get_document_text')
    @mock.patch('document_converter.REPORTLAB_AVAILABLE', True)
    def test_convert_document_to_pdf_for_hybrid_no_text(self, mock_get_text):
        """Test convert_document_to_pdf_for_hybrid when text extraction fails."""
        mock_get_text.return_value = None
        
        result = document_converter.convert_document_to_pdf_for_hybrid('test.docx')
        self.assertIsNone(result)
    
    @mock.patch('document_converter.get_document_text')
    @mock.patch('document_converter.REPORTLAB_AVAILABLE', True)
    @mock.patch('document_converter.canvas')
    @mock.patch('document_converter.tempfile')
    def test_convert_document_to_pdf_for_hybrid_success(self, mock_tempfile, mock_canvas, mock_get_text):
        """Test successful PDF conversion for hybrid processing."""
        # Mock the text extraction
        mock_get_text.return_value = "Test invoice content\nLine 2\nLine 3"
        
        # Mock temporary file creation
        mock_temp_file = mock.MagicMock()
        mock_temp_file.name = '/tmp/test.pdf'
        mock_tempfile.NamedTemporaryFile.return_value = mock_temp_file
        
        # Mock canvas operations
        mock_canvas_instance = mock.MagicMock()
        mock_canvas.Canvas.return_value = mock_canvas_instance
        
        # Mock os.path.getsize
        with mock.patch('os.path.getsize', return_value=1024):
            result = document_converter.convert_document_to_pdf_for_hybrid('test.docx')
        
        # Verify the result
        self.assertEqual(result, '/tmp/test.pdf')
        
        # Verify canvas operations were called
        mock_canvas.Canvas.assert_called_once()
        mock_canvas_instance.save.assert_called_once()

if __name__ == '__main__':
    print("Running document converter tests...")
    unittest.main()
