import sqlite3
import os

# Connect to database
conn = sqlite3.connect('invoicer_app/ui/data/invoices.db')
cursor = conn.execute('SELECT id, filename, pdf_path FROM invoice_reviews LIMIT 5')
rows = cursor.fetchall()

print("Database records:")
for r in rows:
    print(f'ID: {r[0]}, Filename: {r[1]}, PDF Path: {r[2]}')
    if r[2]:  # If pdf_path is not None
        exists = os.path.exists(r[2])
        print(f'  File exists: {exists}')
        if not exists:
            # Check if file exists in monitored_invoices with different path
            alt_path = f'invoicer_app/monitored_invoices/{r[1]}'
            alt_exists = os.path.exists(alt_path)
            print(f'  Alternative path exists: {alt_exists} ({alt_path})')
    print()

conn.close() 