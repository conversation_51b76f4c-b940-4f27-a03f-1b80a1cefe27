Invoice: test_invoice.xlsx
Processing Time: 2025-07-14 23:39:36
AI Provider: openai
AI Confidence: 0.95
Extracted Data:
  invoice_id: INV-2024-002
  vendor_name: Excel Test Company
  invoice_date: 2024-01-16
  due_date: None
  subtotal_eur: 1234.56
  vat_amount_eur: 234.56
  vat_rate: 19.0
  total_amount_eur: 1469.12
  business_unit: None
  original_currency: EUR
  original_total_amount: 1469.12
  original_subtotal: 1234.56
  original_vat_amount: 234.56
  is_duplicate: None
  processing_notes_ref: 
  ai_confidence_score: 0.95
  processing_notes: HYBRID PROCESSING: Text method: Extracted by OpenAI model gpt-4.1-mini. Session: 19d03bcd. Duration: 2.23s. Cost: $0.000107 | File method: Processed via OpenAI Responses API with file upload. Model: gpt-4.1-mini, Session: 9a2eed4f, Response: resp_687587217fb481998c2d1189534d9999044758de669b5aa9, Status: completed, File ID: file-9qh7vQUhtYTZrSs6h7vnsE, Tokens: 762, Cost: $0.000107, Duration: 2.53s
  responses_api_session_id: 9a2eed4f
  responses_api_response_id: resp_687587217fb481998c2d1189534d9999044758de669b5aa9
  responses_api_model_used: gpt-4.1-mini
  responses_api_input_tokens: 658
  responses_api_output_tokens: 104
  responses_api_total_tokens: 762
  responses_api_estimated_cost_usd: 0.00010740000000000001
  responses_api_file_id: file-9qh7vQUhtYTZrSs6h7vnsE
  responses_api_file_size_bytes: 1718
  comparison_notes: Agreement: 1.00. All fields match between methods.
  field_agreement_score: 1.0
  extraction_methods_used: text_and_file

Notes: HYBRID PROCESSING: Text method: Extracted by OpenAI model gpt-4.1-mini. Session: 19d03bcd. Duration: 2.23s. Cost: $0.000107 | File method: Processed via OpenAI Responses API with file upload. Model: gpt-4.1-mini, Session: 9a2eed4f, Response: resp_687587217fb481998c2d1189534d9999044758de669b5aa9, Status: completed, File ID: file-9qh7vQUhtYTZrSs6h7vnsE, Tokens: 762, Cost: $0.000107, Duration: 2.53s
