#!/usr/bin/env python3
"""
Debug script to test different rotation approaches and understand OSD results.
"""

import sys
import os
import tempfile
import shutil

# Add src directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from pdf2image import convert_from_path
from src.pdf_orientation import detect_text_orientation_osd
from src.config_loader import POPPLER_PATH
import PyPDF2

def test_all_rotations(pdf_path):
    """Test all possible rotations and see OSD results."""
    
    print(f"Testing all rotations for: {os.path.basename(pdf_path)}")
    print("=" * 60)
    
    # Convert original PDF to image and test OSD
    print("1. Original PDF OSD:")
    images = convert_from_path(pdf_path, first_page=1, last_page=1, poppler_path=POPPLER_PATH, dpi=200)
    if images:
        osd_result = detect_text_orientation_osd(images[0])
        print(f"   Orientation: {osd_result['orientation']}°")
        print(f"   Rotate needed: {osd_result['rotate']}°")
        print(f"   Script: {osd_result['script']}")
        print(f"   Confidence: {osd_result['confidence']:.1f}")
    
    # Test different rotation amounts
    test_rotations = [90, 180, 270, -90]
    
    for test_rotation in test_rotations:
        print(f"\n2. Testing {test_rotation}° rotation:")
        
        # Create rotated PDF
        temp_fd, temp_path = tempfile.mkstemp(suffix='.pdf', prefix=f'test_rot_{test_rotation}_')
        os.close(temp_fd)
        
        try:
            # Apply rotation
            with open(pdf_path, 'rb') as input_file:
                pdf_reader = PyPDF2.PdfReader(input_file)
                pdf_writer = PyPDF2.PdfWriter()
                
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    page.rotate(test_rotation)
                    pdf_writer.add_page(page)
                
                with open(temp_path, 'wb') as output_file:
                    pdf_writer.write(output_file)
            
            # Test OSD on rotated PDF
            rotated_images = convert_from_path(temp_path, first_page=1, last_page=1, poppler_path=POPPLER_PATH, dpi=200)
            if rotated_images:
                osd_result = detect_text_orientation_osd(rotated_images[0])
                print(f"   After {test_rotation}° rotation:")
                print(f"   Orientation: {osd_result['orientation']}°")
                print(f"   Rotate needed: {osd_result['rotate']}°")
                print(f"   Script: {osd_result['script']}")
                print(f"   Confidence: {osd_result['confidence']:.1f}")
                
                # Save this version for manual inspection
                output_path = f"test_rotation_{test_rotation}_degrees.pdf"
                shutil.copy2(temp_path, output_path)
                print(f"   Saved as: {output_path}")
            
        except Exception as e:
            print(f"   Error: {e}")
        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)

def main():
    """Main function."""
    if len(sys.argv) != 2:
        print("Usage: python test_rotation_debug.py <pdf_file_path>")
        return 1
    
    pdf_path = sys.argv[1]
    
    # Make path absolute if relative
    if not os.path.isabs(pdf_path):
        pdf_path = os.path.abspath(pdf_path)
    
    if not os.path.exists(pdf_path):
        print(f"Error: PDF file not found: {pdf_path}")
        return 1
    
    test_all_rotations(pdf_path)
    
    print("\n" + "=" * 60)
    print("Test completed! Check the generated PDF files to see which rotation looks correct.")
    print("Files created:")
    for rotation in [90, 180, 270, -90]:
        filename = f"test_rotation_{rotation}_degrees.pdf"
        if os.path.exists(filename):
            print(f"  - {filename}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
