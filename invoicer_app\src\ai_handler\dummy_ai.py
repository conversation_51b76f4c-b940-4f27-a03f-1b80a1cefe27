"""
Dummy AI handler module for testing and development purposes.

This module provides a mock AI implementation that returns predefined invoice data
without making actual API calls. It's useful for testing the application pipeline,
development scenarios where AI services are unavailable, and cost-free operation
during development.

Key Features:
    - Consistent dummy data generation
    - No external API dependencies
    - Immediate response (no network delays)
    - Configurable test data scenarios
    - Full compatibility with the main AI interface

Author: MacInvoicer Development Team
Version: 1.0.0
"""

import datetime

def extract_invoice_data_dummy(pdf_text_content, filename):
    """
    Generate dummy invoice data for testing purposes.
    
    This function simulates AI-powered invoice data extraction by returning
    predefined test data. It maintains the same interface as the real AI
    handlers to enable seamless testing and development.
    
    Args:
        pdf_text_content (str): PDF text content (ignored in dummy implementation).
        filename (str): Name of the invoice file being processed.
        
            Returns:
        dict: Structured dummy invoice data containing:
            - invoice_id: Generated dummy invoice ID with current date
            - vendor_name: Fixed dummy vendor name
            - invoice_date: Current date
            - due_date: Current date + 30 days
            - total_amount_eur: Fixed dummy amount (123.45)
            - vat_amount_eur: Fixed dummy VAT (12.35)
            - business_unit: Fixed dummy business unit (IT Department)
            - original_currency: EUR
            - original_total_amount: Same as total_amount_eur
            - ai_confidence_score: Fixed medium confidence (0.50)
            - processing_notes: Explanation of dummy processing
            
    Note:
        This function is for testing/development only and does not perform
        actual invoice analysis. The returned data is static except for
        date-based fields.
    """
    print(f"AI Handler (Dummy) processing: {filename}")
    # Simulate AI processing delay
    # time.sleep(1)

    # Dummy data - you can customize this for testing
    return {
        "invoice_id": f"INV-DUMMY-{datetime.date.today().strftime('%Y%m%d')}",
        "vendor_name": "Dummy Vendor Inc.",
        "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
        "due_date": (datetime.date.today() + datetime.timedelta(days=30)).strftime("%Y-%m-%d"),
        "subtotal_eur": 100.00,
        "total_amount_eur": 123.45,
        "vat_amount_eur": 23.45,
        "vat_rate": 23.0,
        "business_unit": "IT Department",
        "original_currency": "EUR",
        "original_total_amount": 123.45,
        "original_subtotal": 100.00,
        "original_vat_amount": 23.45,
        "is_duplicate": False,
        "ai_confidence_score": 0.50, # Dummy confidence
        "processing_notes": "Successfully processed by Dummy AI. No real analysis performed."
    }