<!DOCTYPE html>
<html>
<head>
    <title>{% block title %}Invoice Review Dashboard{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <style>
        .navigation {
            margin-bottom: 20px;
        }
        
        .navigation a {
            color: #007bff;
            text-decoration: none;
            margin-right: 20px;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background 0.3s ease;
        }
        
        .navigation a:hover {
            background: #e7f3ff;
        }
        
        .navigation a.active {
            background: #007bff;
            color: white;
        }

        /* Dify Chatbot Custom Styles - Modern Theme */
        :root {
            --dify-chatbot-bubble-button-bg-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --dify-chatbot-bubble-button-width: 60px;
            --dify-chatbot-bubble-button-height: 60px;
            --dify-chatbot-bubble-button-border-radius: 30px;
            --dify-chatbot-bubble-button-box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4), 0 4px 16px rgba(118, 75, 162, 0.3);
            --dify-chatbot-bubble-button-hover-transform: scale(1.05) translateY(-2px);
            --dify-chatbot-bubble-button-bottom: 1.5rem;
            --dify-chatbot-bubble-button-right: 1.5rem;
        }

        #dify-chatbot-bubble-button {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            border: 2px solid rgba(255, 255, 255, 0.1) !important;
            backdrop-filter: blur(10px) !important;
        }

        #dify-chatbot-bubble-button:hover {
            box-shadow: 0 12px 40px rgba(102, 126, 234, 0.5), 0 8px 20px rgba(118, 75, 162, 0.4) !important;
        }

        #dify-chatbot-bubble-window {
            width: 28rem !important;
            height: 42rem !important;
            border-radius: 1rem !important;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
            backdrop-filter: blur(16px) !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
            overflow: hidden !important;
        }

        /* Modern glassmorphism effect for the chat window */
        #dify-chatbot-bubble-window iframe {
            border-radius: 1rem !important;
            background: rgba(255, 255, 255, 0.1) !important;
        }

        /* Force dark text in chat bubbles - targeting specific classes */
        #dify-chatbot-bubble-window * {
            color-scheme: light !important;
        }

        /* Try to override iframe content styling */
        #dify-chatbot-bubble-window iframe {
            filter: none !important;
        }

        /* Inject CSS into iframe when possible */
        #dify-chatbot-bubble-window {
            --text-primary: #1a202c !important;
            --text-gray-900: #1a202c !important;
            --bg-user-message: #667eea !important;
        }

        /* Animation for smooth appearance */
        @keyframes chatbotSlideIn {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        #dify-chatbot-bubble-window {
            animation: chatbotSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }

        {% block extra_styles %}{% endblock %}
    </style>
    {% block extra_head %}{% endblock %}
</head>
<body>
    <div class="container">
        {% block navigation %}
        <div class="navigation">
            <a href="{{ url_for('index') }}" {% if request.endpoint == 'index' %}class="active"{% endif %}>Review Dashboard</a>
            <a href="{{ url_for('upload_page') }}" {% if request.endpoint == 'upload_page' %}class="active"{% endif %}>Upload & Processing</a>
        </div>
        {% endblock %}
        
        {% with messages = get_flashed_messages(with_categories=true) %}
          {% if messages %}
            <div class="flashed-messages">
              {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
              {% endfor %}
            </div>
          {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <!-- Dify Chatbot -->
    <script>
     window.difyChatbotConfig = {
      token: 'qR1nPJVydd4JJkiy',
      baseUrl: 'http://localhost',
      draggable: false,
      containerProps: {
        style: {
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          width: '60px',
          height: '60px',
          borderRadius: '30px',
          boxShadow: '0 8px 32px rgba(102, 126, 234, 0.4), 0 4px 16px rgba(118, 75, 162, 0.3)',
          border: '2px solid rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(10px)',
        }
      },
      // Add theme configuration to help with text visibility
      theme: {
        primaryColor: '#667eea',
        chatBubbleColor: '#667eea',
        userMessageBackgroundColor: '#667eea',
        userMessageTextColor: '#ffffff',
        assistantMessageBackgroundColor: '#f8f9fa',
        assistantMessageTextColor: '#2d3748',
      },
      systemVariables: {
        // user_id: 'YOU CAN DEFINE USER ID HERE',
        // conversation_id: 'YOU CAN DEFINE CONVERSATION ID HERE, IT MUST BE A VALID UUID',
      },
     }
    </script>
    <!-- Chatbot disabled - uncomment when chatbot server is available
    <script
     src="http://localhost/embed.min.js"
     id="qR1nPJVydd4JJkiy"
     defer>
    </script>
    -->

    <script>
        // Fix iframe text styling after chatbot loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                const iframe = document.querySelector('#dify-chatbot-bubble-window iframe');
                if (iframe) {
                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        const style = iframeDoc.createElement('style');
                        style.textContent = `
                            .text-gray-900, .markdown-body, .markdown-body p {
                                color: #1a202c !important;
                            }
                            .bg-\[\#D1E9FF\]\/50 {
                                background-color: #667eea !important;
                            }
                            .text-text-primary {
                                color: #ffffff !important;
                            }
                        `;
                        iframeDoc.head.appendChild(style);
                    } catch (e) {
                        console.log('Cannot inject CSS into iframe due to CORS');
                    }
                }
            }, 2000);
        });
    </script>

    {% block extra_scripts %}{% endblock %}
</body>
</html> 