# MacInvoicer

**Automated Invoice Processing Tool with AI-Powered Data Extraction**

MacInvoicer is a comprehensive Python application that automatically monitors directories for new PDF invoice files, extracts structured data using AI, and organizes the results in monthly Excel spreadsheets. The tool provides cost-effective invoice processing with comprehensive logging, error handling, and flexible configuration options.

---

## 🚀 Features

### Core Functionality
- **Automated Directory Monitoring:** Real-time invoice processing using watchdog.
- **Dual PDF Text Extraction:** PyPDF2 for text-based PDFs with OCR fallback for scanned documents.
- **AI-Powered Data Extraction:** OpenAI Responses API integration for structured field extraction.
- **Monthly Spreadsheet Organization:** Automatic Excel file creation with running totals.
- **Comprehensive Logging:** Detailed audit trails, session tracking, and cost analysis.

### AI & Processing
- **Hybrid Processing:** Dual extraction using both text analysis and direct PDF upload for higher confidence.
- **Multiple Model Support:** Cost-effective models (gpt-4.1-nano, gpt-4o-mini, etc.).
- **Cost Tracking:** Real-time token usage and cost calculation.
- **Session Management:** Unique session IDs for tracking and debugging.
- **Advanced Confidence Scoring:** Hybrid processing with confidence metrics.

### Data Management
- **Structured Output:** Consistent data extraction with standardized fields.
- **Currency Handling:** Multi-currency support with EUR standardization.
- **Running Totals:** Automatic calculation and maintenance of monthly totals.
- **Processing Notes:** Detailed notes for each invoice with confidence scores.

---

## 📋 Prerequisites

**System Requirements**
- Windows 10/11, Linux, or macOS
- Python 3.8 or higher
- Tesseract OCR (for scanned PDF processing)
- Poppler (for PDF to image conversion)

**API Requirements**
- OpenAI API Key (for AI-powered extraction)
- Valid OpenAI subscription

---

## 🛠️ Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd macinvoicer
```

### 2. Create Virtual Environment
```bash
python -m venv .venv
# Windows
.venv\Scripts\activate
# Linux/macOS
source .venv/bin/activate
```

### 3. Install Python Dependencies
```bash
pip install -r requirements.txt
```

### 4. Install System Dependencies

#### Windows
- **Tesseract OCR:** [Download](https://github.com/UB-Mannheim/tesseract/wiki) and install to `C:\Program Files\Tesseract-OCR\`
- **Poppler:** [Download](https://github.com/oschwartz10612/poppler-windows/releases), extract to `C:\Program Files\poppler\`, and add to PATH or `.env`

#### Linux (Ubuntu/Debian)
```bash
sudo apt-get update
sudo apt-get install tesseract-ocr
sudo apt-get install poppler-utils
```

#### macOS
```bash
brew install tesseract
brew install poppler
```

---

## ⚙️ Configuration

1. **Create `.env` file** in the project root and set your OpenAI API key and system paths.
2. **Configure Environment Variables** as needed for deployment.
3. **Supported Models:** List available OpenAI models in `.env`.
4. **Hybrid Processing:** Enable/disable in config files.
5. **Directory Structure:**  
   ```
   macinvoicer/
   ├── monitored_invoices/      # Place PDF invoices here
   ├── output_spreadsheets/     # Monthly Excel files generated here
   ├── processed_invoices_notes/ # Processing notes for each invoice
   ├── logs/                    # Application logs
   └── config/                  # Configuration files
   ```

---

## 🚀 Usage

### Starting the Application
```bash
cd macinvoicer/src
python main.py
```

### Processing Invoices
1. **Start the application** – monitoring begins on `monitored_invoices/`.
2. **Add PDF invoices** – Copy or move PDF files to the monitored folder.
3. **Automatic processing** – The app will:
    - Detect new files
    - Extract PDF text (OCR if needed)
    - Send data to AI for extraction
    - Append to monthly spreadsheets
    - Generate audit notes

### Stopping the Application
Press `Ctrl+C` to stop gracefully.

---

## 📊 Output Structure

### Monthly Spreadsheets
Files: `YYYY-MM_invoices.xlsx`

**Columns:**
- Processing_DateTime
- Invoice_Filename
- Invoice_ID
- Vendor_Name
- Invoice_Date
- Due_Date
- Total_Amount_EUR
- VAT_Amount_EUR
- Original_Currency
- Original_Total_Amount
- AI_Confidence_Score
- Running_Total_EUR

### Processing Notes
Each invoice gets a note file:
```
invoice_name_processing_note.txt
```
Contains:
- Processing timestamp and AI provider
- Extracted data summary
- AI confidence score
- Any processing issues

---

## 🔧 Development

### Project Structure
```
macinvoicer/
├── src/
│   ├── main.py                 # Main application entry point
│   ├── config_loader.py        # Config management
│   ├── app_logger.py           # Logging config
│   ├── invoice_parser.py       # PDF extraction
│   ├── spreadsheet_manager.py  # Excel ops
│   └── ai_handler/
│       ├── openai_ai.py        # OpenAI integration
│       └── dummy_ai.py         # Testing/mock
├── tests/                      # Tests
├── docs/                       # Docs
├── config/                     # Config files
├── requirements.txt            # Dependencies
└── README.md                   # This file
```

### Running Tests
```bash
cd macinvoicer
python -m pytest tests/
```

---

## 📚 Additional Documentation

- [Installation Guide](docs/INSTALLATION.md)
- [Configuration Guide](docs/CONFIGURATION.md)
- [User Guide](docs/USER_GUIDE.md)
- [Developer Guide](docs/DEVELOPER_GUIDE.md)
- [API Reference](docs/API_REFERENCE.md)

---

## 🔒 Security

- Store API keys in `.env` (never commit to version control)
- Use environment variables for production
- Rotate API keys regularly
- Invoice data is processed locally except for AI API calls
- No data is stored on AI provider servers (when store=False)
- Processing notes contain no sensitive data

---

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

---

## 📄 License

This project is licensed under the MIT License.

---

## 📞 Support

- Check troubleshooting in this README and `docs/`
- Open an issue for bug reports or feature requests

---

## 🏷️ Version History

- **v1.0.0** - Initial release with OpenAI integration

---

### 📦 Typical Workflow Diagram

```
![Workflow Diagram](/invoicer_app/docs/docs_workflow-diagram.svg)
```

---
