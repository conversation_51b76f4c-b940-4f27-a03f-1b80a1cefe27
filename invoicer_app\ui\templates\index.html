{% extends "base.html" %}

{% block title %}Invoice Review Dashboard{% endblock %}

{% block extra_head %}
{{ super() }}
<style>
/* Dashboard Styles */
.filter-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.status-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.status-btn {
    padding: 10px 20px;
    border: 2px solid #dee2e6;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-btn:hover {
    border-color: #007bff;
    background: #e3f2fd;
}

.status-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.count-badge {
    background: rgba(0,0,0,0.2);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

.status-btn.active .count-badge {
    background: rgba(255,255,255,0.3);
}

.additional-filters {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-weight: 500;
    font-size: 14px;
    color: #495057;
}

.filter-group select,
.filter-group input {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

.actions-section {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

#export-format {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    border-radius: 4px;
    padding: 2px 6px;
}

.results-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px 15px;
    background: #e9ecef;
    border-radius: 6px;
    font-weight: 500;
}

.table-container {
    overflow-x: auto;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 20px;
}

.invoice-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.invoice-table th {
    background: #f8f9fa;
    padding: 12px;
    text-align: left;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    cursor: pointer;
    user-select: none;
    position: relative;
}

.invoice-table th:hover {
    background: #e9ecef;
}

.sort-indicator {
    position: absolute;
    right: 8px;
    opacity: 0.5;
}

.sort-indicator.asc::after {
    content: "↑";
}

.sort-indicator.desc::after {
    content: "↓";
}

.invoice-table td {
    padding: 12px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
}

.invoice-table tbody tr:hover {
    background: #f8f9fa;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-approved {
    background: #d4edda;
    color: #155724;
}

.status-rejected {
    background: #f8d7da;
    color: #721c24;
}

.pagination-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
}

.pagination-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.pagination-controls button {
    padding: 8px 16px;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 4px;
    cursor: pointer;
}

.pagination-controls button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-controls button:not(:disabled):hover {
    background: #e9ecef;
}

.loading {
    color: #6c757d;
    font-style: italic;
}

@media (max-width: 768px) {
    .additional-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        width: 100%;
    }

    .actions-section {
        flex-direction: column;
        align-items: stretch;
    }

    .results-summary {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
}
</style>
{% endblock %}

{% block content %}
<h1>Invoice Review Dashboard</h1>

<!-- Status Filter Buttons -->
<div class="filter-section">
    <div class="status-buttons">
        <button id="btn-all" class="status-btn active" onclick="filterByStatus('all')">
            All Invoices <span id="count-all" class="count-badge">0</span>
        </button>
        <button id="btn-pending" class="status-btn" onclick="filterByStatus('pending')">
            Pending <span id="count-pending" class="count-badge">0</span>
        </button>
        <button id="btn-approved" class="status-btn" onclick="filterByStatus('approved')">
            Approved <span id="count-approved" class="count-badge">0</span>
        </button>
        <button id="btn-rejected" class="status-btn" onclick="filterByStatus('rejected')">
            Rejected <span id="count-rejected" class="count-badge">0</span>
        </button>
    </div>

    <!-- Additional Filters -->
    <div class="additional-filters">
        <div class="filter-group">
            <label for="date-filter">Date Range:</label>
            <select id="date-filter" onchange="applyFilters()">
                <option value="all">All Time</option>
                <option value="today">Today</option>
                <option value="week">This Week</option>
                <option value="month">This Month</option>
            </select>
        </div>

        <div class="filter-group">
            <label for="vendor-filter">Vendor:</label>
            <select id="vendor-filter" onchange="applyFilters()">
                <option value="all">All Vendors</option>
            </select>
        </div>

        <div class="filter-group">
            <label for="sort-filter">Sort By:</label>
            <select id="sort-filter" onchange="applyFilters()">
                <option value="created_desc">Newest First</option>
                <option value="created_asc">Oldest First</option>
                <option value="filename_asc">Filename A-Z</option>
                <option value="filename_desc">Filename Z-A</option>
                <option value="total_desc">Highest Amount</option>
                <option value="total_asc">Lowest Amount</option>
            </select>
        </div>

        <div class="filter-group">
            <label for="search-filter">Search:</label>
            <input type="text" id="search-filter" placeholder="Search filename, vendor, invoice ID..." onkeyup="applyFilters()">
        </div>
    </div>
</div>

<!-- Export and Actions -->
<div class="actions-section">
    <button class="btn btn-primary" onclick="exportData()">
        <span id="export-text">Export All</span>
        <select id="export-format" onclick="event.stopPropagation()">
            <option value="xlsx">XLSX</option>
            <option value="csv">CSV</option>
        </select>
    </button>
    <a href="{{ url_for('upload_page') }}" class="btn btn-secondary">Upload New Invoices</a>
    <button class="btn btn-info" onclick="refreshData()">Refresh</button>
</div>

<!-- Results Summary -->
<div class="results-summary">
    <span id="results-count">Loading...</span>
    <span id="results-total">Total: €0.00</span>
</div>

<!-- Invoice Table -->
<div class="table-container">
    <table class="invoice-table" id="invoice-table">
        <thead>
            <tr>
                <th onclick="sortTable('filename')">Filename <span class="sort-indicator"></span></th>
                <th onclick="sortTable('vendor_name')">Vendor <span class="sort-indicator"></span></th>
                <th onclick="sortTable('invoice_id')">Invoice ID <span class="sort-indicator"></span></th>
                <th onclick="sortTable('invoice_date')">Date <span class="sort-indicator"></span></th>
                <th onclick="sortTable('total_amount_eur')">Total (EUR) <span class="sort-indicator"></span></th>
                <th onclick="sortTable('vat_rate')">VAT Rate <span class="sort-indicator"></span></th>
                <th onclick="sortTable('status')">Status <span class="sort-indicator"></span></th>
                <th onclick="sortTable('created_at')">Created <span class="sort-indicator"></span></th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody id="invoice-tbody">
            <tr>
                <td colspan="9" style="text-align: center; padding: 20px;">
                    <div class="loading">Loading invoices...</div>
                </td>
            </tr>
        </tbody>
    </table>
</div>

<!-- Pagination -->
<div class="pagination-section">
    <div class="pagination-info">
        <span id="pagination-info">Showing 0 of 0 invoices</span>
    </div>
    <div class="pagination-controls">
        <button id="prev-page" onclick="changePage(-1)" disabled>Previous</button>
        <span id="page-numbers"></span>
        <button id="next-page" onclick="changePage(1)" disabled>Next</button>
    </div>
</div>

{% endblock %}

{% block extra_scripts %}
<script>
// Dashboard State
let allInvoices = [];
let filteredInvoices = [];
let currentFilter = 'all';
let currentPage = 1;
const itemsPerPage = 20;
let currentSort = { field: 'created_at', direction: 'desc' };

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    loadInvoices();
});

// Load all invoices from API
async function loadInvoices() {
    try {
        const response = await fetch('/api/invoices/all');
        const data = await response.json();
        allInvoices = data.invoices || [];

        // Populate vendor filter
        populateVendorFilter();

        // Apply initial filter
        applyFilters();

        // Update counts
        updateCounts();

    } catch (error) {
        console.error('Error loading invoices:', error);
        document.getElementById('invoice-tbody').innerHTML =
            '<tr><td colspan="9" style="text-align: center; color: red;">Error loading invoices</td></tr>';
    }
}

// Filter by status
function filterByStatus(status) {
    currentFilter = status;
    currentPage = 1;

    // Update button states
    document.querySelectorAll('.status-btn').forEach(btn => btn.classList.remove('active'));
    document.getElementById(`btn-${status}`).classList.add('active');

    // Update export text
    const exportText = document.getElementById('export-text');
    exportText.textContent = status === 'all' ? 'Export All' : `Export ${status.charAt(0).toUpperCase() + status.slice(1)}`;

    applyFilters();
}

// Apply all filters
function applyFilters() {
    let filtered = [...allInvoices];

    // Status filter
    if (currentFilter !== 'all') {
        filtered = filtered.filter(invoice => invoice.status === currentFilter);
    }

    // Date filter
    const dateFilter = document.getElementById('date-filter').value;
    if (dateFilter !== 'all') {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

        filtered = filtered.filter(invoice => {
            const invoiceDate = new Date(invoice.created_at);

            switch (dateFilter) {
                case 'today':
                    return invoiceDate >= today;
                case 'week':
                    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                    return invoiceDate >= weekAgo;
                case 'month':
                    const monthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
                    return invoiceDate >= monthAgo;
                default:
                    return true;
            }
        });
    }

    // Vendor filter
    const vendorFilter = document.getElementById('vendor-filter').value;
    if (vendorFilter !== 'all') {
        filtered = filtered.filter(invoice => invoice.vendor_name === vendorFilter);
    }

    // Search filter
    const searchTerm = document.getElementById('search-filter').value.toLowerCase();
    if (searchTerm) {
        filtered = filtered.filter(invoice =>
            (invoice.filename || '').toLowerCase().includes(searchTerm) ||
            (invoice.vendor_name || '').toLowerCase().includes(searchTerm) ||
            (invoice.invoice_id || '').toLowerCase().includes(searchTerm)
        );
    }

    // Sort
    const sortFilter = document.getElementById('sort-filter').value;
    const [field, direction] = sortFilter.split('_');
    currentSort = { field, direction };

    filtered.sort((a, b) => {
        let aVal = a[field] || '';
        let bVal = b[field] || '';

        // Handle numeric fields
        if (field === 'total_amount_eur' || field === 'vat_rate') {
            aVal = parseFloat(aVal) || 0;
            bVal = parseFloat(bVal) || 0;
        }

        // Handle dates
        if (field === 'created_at' || field === 'invoice_date') {
            aVal = new Date(aVal);
            bVal = new Date(bVal);
        }

        let result = 0;
        if (aVal < bVal) result = -1;
        if (aVal > bVal) result = 1;

        return direction === 'desc' ? -result : result;
    });

    filteredInvoices = filtered;
    renderTable();
    updateSummary();
}

// Render table with pagination
function renderTable() {
    const tbody = document.getElementById('invoice-tbody');
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageInvoices = filteredInvoices.slice(startIndex, endIndex);

    if (pageInvoices.length === 0) {
        tbody.innerHTML = '<tr><td colspan="9" style="text-align: center; padding: 20px;">No invoices found</td></tr>';
        return;
    }

    tbody.innerHTML = pageInvoices.map(invoice => {
        let actions = '';
        // Pending and Rejected: Review button
        if (invoice.status === 'pending' || !invoice.status || invoice.status === 'rejected') {
            actions += `<a href="/review/${invoice.id}" class="btn" style="padding: 6px 12px; font-size: 12px;">Review</a>`;
        }
        // Approved and All: View + Edit buttons
        else if (currentFilter === 'all' || invoice.status === 'approved') {
            actions += `<a href="/review/${invoice.id}" class="btn" style="padding: 6px 12px; font-size: 12px;">View</a>`;
            actions += `<a href="/review/${invoice.id}?edit=1" class="btn btn-warning" style="padding: 6px 12px; font-size: 12px; margin-left: 4px;">Edit</a>`;
        }
        // Reviewed (not pending/approved/rejected): View button
        else {
            actions += `<a href="/review/${invoice.id}" class="btn" style="padding: 6px 12px; font-size: 12px;">View</a>`;
        }
        return `
        <tr>
            <td>${invoice.filename || ''}</td>
            <td>${invoice.vendor_name || ''}</td>
            <td>${invoice.invoice_id || ''}</td>
            <td>${invoice.invoice_date || ''}</td>
            <td>€${(invoice.total_amount_eur || 0).toFixed(2)}</td>
            <td>${invoice.vat_rate ? invoice.vat_rate + '%' : ''}</td>
            <td><span class="status-badge status-${invoice.status || 'pending'}">${(invoice.status || 'pending').toUpperCase()}</span></td>
            <td>${invoice.created_at ? new Date(invoice.created_at).toLocaleDateString() : ''}</td>
            <td>${actions}</td>
        </tr>
        `;
    }).join('');

    updatePagination();
}

// Update pagination controls
function updatePagination() {
    const totalPages = Math.ceil(filteredInvoices.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage + 1;
    const endIndex = Math.min(currentPage * itemsPerPage, filteredInvoices.length);

    document.getElementById('pagination-info').textContent =
        `Showing ${startIndex}-${endIndex} of ${filteredInvoices.length} invoices`;

    document.getElementById('prev-page').disabled = currentPage <= 1;
    document.getElementById('next-page').disabled = currentPage >= totalPages;

    // Generate page numbers
    const pageNumbers = document.getElementById('page-numbers');
    let pages = '';

    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        pages += `<button onclick="goToPage(${i})" ${i === currentPage ? 'style="background: #007bff; color: white;"' : ''}>${i}</button>`;
    }

    pageNumbers.innerHTML = pages;
}

// Change page
function changePage(direction) {
    const totalPages = Math.ceil(filteredInvoices.length / itemsPerPage);
    const newPage = currentPage + direction;

    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        renderTable();
    }
}

// Go to specific page
function goToPage(page) {
    currentPage = page;
    renderTable();
}

// Update summary
function updateSummary() {
    const total = filteredInvoices.reduce((sum, invoice) => sum + (invoice.total_amount_eur || 0), 0);
    document.getElementById('results-count').textContent = `${filteredInvoices.length} invoices found`;
    document.getElementById('results-total').textContent = `Total: €${total.toFixed(2)}`;
}

// Update counts
function updateCounts() {
    const counts = {
        all: allInvoices.length,
        pending: allInvoices.filter(i => !i.status || i.status === 'pending').length,
        approved: allInvoices.filter(i => i.status === 'approved').length,
        rejected: allInvoices.filter(i => i.status === 'rejected').length
    };

    Object.keys(counts).forEach(status => {
        document.getElementById(`count-${status}`).textContent = counts[status];
    });
}

// Populate vendor filter
function populateVendorFilter() {
    const vendors = [...new Set(allInvoices.map(i => i.vendor_name).filter(Boolean))].sort();
    const vendorSelect = document.getElementById('vendor-filter');

    vendorSelect.innerHTML = '<option value="all">All Vendors</option>' +
        vendors.map(vendor => `<option value="${vendor}">${vendor}</option>`).join('');
}

// Export data
async function exportData() {
    const format = document.getElementById('export-format').value;
    const status = currentFilter;

    try {
        const response = await fetch(`/api/export/${status}?format=${format}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                filters: {
                    status: currentFilter,
                    date: document.getElementById('date-filter').value,
                    vendor: document.getElementById('vendor-filter').value,
                    search: document.getElementById('search-filter').value
                }
            })
        });

        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `invoices_${status}_${new Date().toISOString().split('T')[0]}.${format}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        } else {
            alert('Export failed. Please try again.');
        }
    } catch (error) {
        console.error('Export error:', error);
        alert('Export failed. Please try again.');
    }
}

// Refresh data
function refreshData() {
    loadInvoices();
}

// Sort table
function sortTable(field) {
    if (currentSort.field === field) {
        currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
    } else {
        currentSort.field = field;
        currentSort.direction = 'asc';
    }

    // Update sort indicators
    document.querySelectorAll('.sort-indicator').forEach(indicator => {
        indicator.className = 'sort-indicator';
    });

    const indicator = document.querySelector(`th[onclick="sortTable('${field}')"] .sort-indicator`);
    if (indicator) {
        indicator.className = `sort-indicator ${currentSort.direction}`;
    }

    // Update sort filter to match
    document.getElementById('sort-filter').value = `${field}_${currentSort.direction}`;

    applyFilters();
}
</script>
{% endblock %}