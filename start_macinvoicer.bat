@echo off
echo === MacInvoicer Startup Script ===
echo Starting MacInvoicer application...

REM Get the directory where this batch file is located
set SCRIPT_DIR=%~dp0
echo Script directory: %SCRIPT_DIR%

REM Define paths
set VENV_PATH=%SCRIPT_DIR%.venv
set PYTHON_EXE=%VENV_PATH%\Scripts\python.exe
set MAIN_SCRIPT=%SCRIPT_DIR%invoicer_app\src\main.py
set APP_SCRIPT=%SCRIPT_DIR%invoicer_app\ui\app.py

REM Check if virtual environment exists
if not exist "%VENV_PATH%" (
    echo ERROR: Virtual environment not found at: %VENV_PATH%
    echo Please create a virtual environment first by running:
    echo python -m venv .venv
    echo Then install requirements:
    echo .venv\Scripts\activate ^&^& pip install -r invoicer_app\requirements.txt
    pause
    exit /b 1
)

REM Check if main.py exists
if not exist "%MAIN_SCRIPT%" (
    echo ERROR: main.py not found at: %MAIN_SCRIPT%
    pause
    exit /b 1
)

REM Check if app.py exists
if not exist "%APP_SCRIPT%" (
    echo ERROR: app.py not found at: %APP_SCRIPT%
    pause
    exit /b 1
)

echo Virtual environment found: %VENV_PATH%
echo Python executable: %PYTHON_EXE%

REM Start main.py in a new command window
echo Starting main.py (invoice processing service)...
start "MacInvoicer - Main Process" cmd /k "cd /d "%SCRIPT_DIR%" && "%VENV_PATH%\Scripts\activate.bat" && echo Virtual environment activated for main.py && echo Starting invoice processing service... && "%PYTHON_EXE%" "%MAIN_SCRIPT%""

REM Wait a moment for main.py to initialize
echo Waiting 3 seconds for main.py to initialize...
timeout /t 3 /nobreak >nul

REM Start app.py in current window
echo Starting app.py (web UI)...
cd /d "%SCRIPT_DIR%"
call "%VENV_PATH%\Scripts\activate.bat"
echo Virtual environment activated for app.py
echo Working directory: %CD%
echo Starting Flask web application...
echo Web UI will be available at: http://127.0.0.1:5000
echo Press Ctrl+C to stop the web server
echo.

"%PYTHON_EXE%" "%APP_SCRIPT%"

echo MacInvoicer has stopped.
pause
