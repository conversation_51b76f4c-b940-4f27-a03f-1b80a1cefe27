#!/usr/bin/env python3
"""
MacInvoicer Clean Startup Script
This script cleans all data (files and database) before starting the invoice processing service and web UI.

WARNING: This will permanently delete:
- All files in monitored_invoices/
- All files in processed_invoices_notes/
- All files in output_spreadsheets/
- All data from database tables (invoice_imports, approved_invoices, rejected_invoices)
"""

import os
import sys
import subprocess
import time
import signal
import sqlite3
import shutil
from pathlib import Path

def print_colored(message, color="white"):
    """Print colored messages to console"""
    colors = {
        "red": "\033[91m",
        "green": "\033[92m", 
        "yellow": "\033[93m",
        "blue": "\033[94m",
        "cyan": "\033[96m",
        "white": "\033[97m",
        "magenta": "\033[95m",
        "reset": "\033[0m"
    }
    print(f"{colors.get(color, colors['white'])}{message}{colors['reset']}")

def confirm_cleanup():
    """Ask user to confirm the cleanup operation"""
    print_colored("⚠️  WARNING: This will permanently delete ALL data!", "red")
    print_colored("This includes:", "yellow")
    print_colored("  • All files in monitored_invoices/", "white")
    print_colored("  • All files in processed_invoices_notes/", "white")
    print_colored("  • All files in output_spreadsheets/", "white")
    print_colored("  • All database records (invoice_imports, approved_invoices, rejected_invoices)", "white")
    print()
    
    response = input("Are you sure you want to continue? Type 'YES' to confirm: ")
    return response.strip().upper() == 'YES'

def clean_directory(directory_path, description):
    """Clean all files from a directory"""
    try:
        if directory_path.exists():
            file_count = len(list(directory_path.glob('*')))
            if file_count > 0:
                print_colored(f"Cleaning {description}... ({file_count} files)", "yellow")
                for item in directory_path.iterdir():
                    if item.is_file():
                        item.unlink()
                        print_colored(f"  Deleted: {item.name}", "cyan")
                    elif item.is_dir():
                        shutil.rmtree(item)
                        print_colored(f"  Deleted directory: {item.name}", "cyan")
                print_colored(f"✅ {description} cleaned successfully", "green")
            else:
                print_colored(f"✅ {description} already empty", "green")
        else:
            print_colored(f"📁 {description} directory doesn't exist, creating...", "yellow")
            directory_path.mkdir(parents=True, exist_ok=True)
            print_colored(f"✅ Created {description} directory", "green")
    except Exception as e:
        print_colored(f"❌ Error cleaning {description}: {e}", "red")
        return False
    return True

def clean_database(db_path):
    """Clean all data from database tables"""
    try:
        if db_path.exists():
            print_colored("Cleaning database tables...", "yellow")
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Get record counts before deletion
            try:
                cursor.execute("SELECT COUNT(*) FROM invoice_imports")
                imports_count = cursor.fetchone()[0]
                cursor.execute("SELECT COUNT(*) FROM approved_invoices")
                approved_count = cursor.fetchone()[0]

                # Check if rejected_invoices table exists
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='rejected_invoices'")
                rejected_table_exists = cursor.fetchone() is not None
                rejected_count = 0
                if rejected_table_exists:
                    cursor.execute("SELECT COUNT(*) FROM rejected_invoices")
                    rejected_count = cursor.fetchone()[0]

                print_colored(f"  Found {imports_count} records in invoice_imports", "cyan")
                print_colored(f"  Found {approved_count} records in approved_invoices", "cyan")
                if rejected_table_exists:
                    print_colored(f"  Found {rejected_count} records in rejected_invoices", "cyan")

                # Delete all records
                cursor.execute("DELETE FROM invoice_imports")
                cursor.execute("DELETE FROM approved_invoices")
                if rejected_table_exists:
                    cursor.execute("DELETE FROM rejected_invoices")

                # Reset auto-increment counters
                cursor.execute("DELETE FROM sqlite_sequence WHERE name='invoice_imports'")
                cursor.execute("DELETE FROM sqlite_sequence WHERE name='approved_invoices'")
                if rejected_table_exists:
                    cursor.execute("DELETE FROM sqlite_sequence WHERE name='rejected_invoices'")

                conn.commit()
                print_colored(f"✅ Database cleaned successfully", "green")
                print_colored(f"  Deleted {imports_count} records from invoice_imports", "cyan")
                print_colored(f"  Deleted {approved_count} records from approved_invoices", "cyan")
                if rejected_table_exists:
                    print_colored(f"  Deleted {rejected_count} records from rejected_invoices", "cyan")
                
            except sqlite3.OperationalError as e:
                if "no such table" in str(e).lower():
                    print_colored("✅ Database tables don't exist yet (will be created on first run)", "green")
                else:
                    raise e
            finally:
                conn.close()
        else:
            print_colored("✅ Database doesn't exist yet (will be created on first run)", "green")
    except Exception as e:
        print_colored(f"❌ Error cleaning database: {e}", "red")
        return False
    return True

def main():
    print_colored("=== MacInvoicer Clean Startup Script ===", "green")
    print_colored("This script will clean ALL data before starting MacInvoicer", "magenta")
    print()
    
    # Get script directory
    script_dir = Path(__file__).parent.absolute()
    print_colored(f"Script directory: {script_dir}", "cyan")
    
    # Define paths
    invoicer_app_dir = script_dir / "invoicer_app"
    monitored_invoices = invoicer_app_dir / "monitored_invoices"
    processed_notes = invoicer_app_dir / "processed_invoices_notes"
    output_spreadsheets = invoicer_app_dir / "output_spreadsheets"
    database_path = invoicer_app_dir / "ui" / "data" / "invoices.db"
    
    venv_path = script_dir / ".venv"
    if os.name == 'nt':  # Windows
        python_exe = venv_path / "Scripts" / "python.exe"
    else:  # Unix/Linux/Mac
        python_exe = venv_path / "bin" / "python"
    
    main_script = invoicer_app_dir / "src" / "main.py"
    app_script = invoicer_app_dir / "ui" / "app.py"
    
    # Confirm cleanup operation
    if not confirm_cleanup():
        print_colored("❌ Cleanup cancelled by user", "red")
        return 1
    
    print()
    print_colored("🧹 Starting cleanup process...", "yellow")
    print()
    
    # Perform cleanup operations
    cleanup_success = True
    
    # Clean directories
    cleanup_success &= clean_directory(monitored_invoices, "monitored_invoices")
    cleanup_success &= clean_directory(processed_notes, "processed_invoices_notes")
    cleanup_success &= clean_directory(output_spreadsheets, "output_spreadsheets")
    
    # Clean database
    cleanup_success &= clean_database(database_path)
    
    if not cleanup_success:
        print_colored("❌ Some cleanup operations failed. Check errors above.", "red")
        return 1
    
    print()
    print_colored("🎉 Cleanup completed successfully!", "green")
    print_colored("All data has been removed. Starting MacInvoicer...", "yellow")
    print()
    
    # Check if virtual environment exists
    if not venv_path.exists():
        print_colored(f"ERROR: Virtual environment not found at: {venv_path}", "red")
        print_colored("Please create a virtual environment first by running:", "yellow")
        print_colored("python -m venv .venv", "white")
        print_colored("Then install requirements:", "yellow")
        if os.name == 'nt':
            print_colored(".venv\\Scripts\\activate && pip install -r invoicer_app\\requirements.txt", "white")
        else:
            print_colored("source .venv/bin/activate && pip install -r invoicer_app/requirements.txt", "white")
        return 1
    
    # Check if scripts exist
    if not main_script.exists():
        print_colored(f"ERROR: main.py not found at: {main_script}", "red")
        return 1
    
    if not app_script.exists():
        print_colored(f"ERROR: app.py not found at: {app_script}", "red")
        return 1
    
    print_colored(f"Virtual environment found: {venv_path}", "green")
    print_colored(f"Python executable: {python_exe}", "green")
    
    # Store process references
    main_process = None
    app_process = None
    
    def cleanup_processes():
        """Clean up child processes"""
        if main_process and main_process.poll() is None:
            print_colored("Stopping main process...", "yellow")
            main_process.terminate()
            try:
                main_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                main_process.kill()
        
        if app_process and app_process.poll() is None:
            print_colored("Stopping app process...", "yellow")
            app_process.terminate()
            try:
                app_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                app_process.kill()
    
    def signal_handler(signum, frame):
        """Handle Ctrl+C gracefully"""
        print_colored("\nReceived interrupt signal. Shutting down...", "yellow")
        cleanup_processes()
        sys.exit(0)
    
    # Set up signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    if hasattr(signal, 'SIGTERM'):
        signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Start main.py process
        print_colored("Starting main.py (invoice processing service)...", "yellow")
        main_process = subprocess.Popen(
            [str(python_exe), str(main_script)],
            cwd=str(script_dir),
            creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
        )
        print_colored("Main.py started in background", "green")
        
        # Wait for main.py to initialize
        print_colored("Waiting 3 seconds for main.py to initialize...", "yellow")
        time.sleep(3)
        
        # Start app.py process
        print_colored("Starting app.py (web UI)...", "yellow")
        print_colored("Web UI will be available at: http://127.0.0.1:5000", "cyan")
        print_colored("Press Ctrl+C to stop both services", "yellow")
        print()
        
        app_process = subprocess.Popen(
            [str(python_exe), str(app_script)],
            cwd=str(script_dir)
        )
        
        # Wait for app process to complete
        app_process.wait()
        
    except KeyboardInterrupt:
        print_colored("\nKeyboard interrupt received", "yellow")
    except Exception as e:
        print_colored(f"ERROR: An error occurred during startup: {e}", "red")
        return 1
    finally:
        cleanup_processes()
    
    print_colored("MacInvoicer has stopped.", "yellow")
    return 0

if __name__ == "__main__":
    sys.exit(main())
