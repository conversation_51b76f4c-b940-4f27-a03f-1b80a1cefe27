#!/usr/bin/env python3
"""
Tesseract Diagnostic Script for MacInvoicer
This script helps diagnose Tesseract installation and configuration issues.
"""

import os
import sys
import subprocess
from pathlib import Path

def test_tesseract_installation():
    """Test Tesseract installation and configuration."""
    
    print("=== TESSERACT DIAGNOSTIC SCRIPT ===")
    print()
    
    # Check if we're in the right directory
    current_dir = Path.cwd()
    print(f"Current directory: {current_dir}")
    
    # Try to import required modules
    print("1. Testing Python module imports...")
    try:
        import pytesseract
        print("   ✓ pytesseract module imported successfully")
    except ImportError as e:
        print(f"   ✗ Failed to import pytesseract: {e}")
        return False
    
    try:
        from PIL import Image
        print("   ✓ PIL (Pillow) module imported successfully")
    except ImportError as e:
        print(f"   ✗ Failed to import PIL: {e}")
        return False
    
    try:
        from pdf2image import convert_from_path
        print("   ✓ pdf2image module imported successfully")
    except ImportError as e:
        print(f"   ✗ Failed to import pdf2image: {e}")
        return False
    
    print()
    
    # Check .env configuration
    print("2. Checking .env configuration...")
    env_path = Path("invoicer_app/config/.env")
    if env_path.exists():
        print(f"   ✓ .env file found at: {env_path}")
        
        # Read .env file
        with open(env_path, 'r') as f:
            env_content = f.read()
        
        tesseract_path = None
        poppler_path = None
        
        for line in env_content.split('\n'):
            if line.startswith('TESSERACT_CMD_PATH='):
                tesseract_path = line.split('=', 1)[1].strip('"')
                print(f"   ✓ TESSERACT_CMD_PATH found: {tesseract_path}")
            elif line.startswith('POPPLER_PATH='):
                poppler_path = line.split('=', 1)[1].strip('"')
                print(f"   ✓ POPPLER_PATH found: {poppler_path}")
        
        if not tesseract_path:
            print("   ⚠ TESSERACT_CMD_PATH not found in .env")
        if not poppler_path:
            print("   ⚠ POPPLER_PATH not found in .env")
    else:
        print(f"   ✗ .env file not found at: {env_path}")
        tesseract_path = None
        poppler_path = None
    
    print()
    
    # Test Tesseract executable
    print("3. Testing Tesseract executable...")
    
    if tesseract_path:
        print(f"   Testing configured path: {tesseract_path}")
        if os.path.exists(tesseract_path):
            print("   ✓ Tesseract executable file exists")
            
            if os.access(tesseract_path, os.X_OK):
                print("   ✓ Tesseract executable has execute permissions")
            else:
                print("   ✗ Tesseract executable does NOT have execute permissions")
                return False
        else:
            print(f"   ✗ Tesseract executable NOT found at: {tesseract_path}")
            return False
    else:
        print("   Testing system PATH...")
        tesseract_path = "tesseract"  # Use system PATH
    
    # Try to run Tesseract version command
    try:
        result = subprocess.run([tesseract_path, "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("   ✓ Tesseract version command successful:")
            print(f"     {result.stdout.split()[1] if len(result.stdout.split()) > 1 else 'Unknown version'}")
        else:
            print(f"   ✗ Tesseract version command failed with return code: {result.returncode}")
            print(f"     stderr: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("   ✗ Tesseract version command timed out")
        return False
    except FileNotFoundError:
        print("   ✗ Tesseract executable not found in PATH")
        return False
    except Exception as e:
        print(f"   ✗ Error running Tesseract: {e}")
        return False
    
    print()
    
    # Test pytesseract configuration
    print("4. Testing pytesseract configuration...")
    
    if tesseract_path and tesseract_path != "tesseract":
        pytesseract.tesseract_cmd = tesseract_path
        print(f"   Set pytesseract.tesseract_cmd to: {tesseract_path}")
    
    try:
        version = pytesseract.get_tesseract_version()
        print(f"   ✓ pytesseract.get_tesseract_version() successful: {version}")
    except Exception as e:
        print(f"   ✗ pytesseract.get_tesseract_version() failed: {e}")
        return False
    
    print()
    
    # Test Poppler (if configured)
    if poppler_path:
        print("5. Testing Poppler configuration...")
        print(f"   Testing configured path: {poppler_path}")
        
        if os.path.exists(poppler_path):
            print("   ✓ Poppler path exists")
            
            # Look for common Poppler executables
            poppler_exes = ['pdftoppm.exe', 'pdftoppm', 'pdfinfo.exe', 'pdfinfo']
            found_exe = False
            
            for exe in poppler_exes:
                exe_path = os.path.join(poppler_path, exe)
                if os.path.exists(exe_path):
                    print(f"   ✓ Found Poppler executable: {exe}")
                    found_exe = True
                    break
            
            if not found_exe:
                print("   ⚠ No Poppler executables found in configured path")
        else:
            print(f"   ✗ Poppler path does NOT exist: {poppler_path}")
    
    print()
    print("=== DIAGNOSTIC COMPLETE ===")
    print()
    print("If all tests passed, Tesseract should work correctly.")
    print("If any tests failed, please address the issues before running MacInvoicer.")
    
    return True

if __name__ == "__main__":
    success = test_tesseract_installation()
    if success:
        print("\n✓ All tests passed! Tesseract should work correctly.")
    else:
        print("\n✗ Some tests failed. Please fix the issues above.")
    
    input("\nPress Enter to exit...")
