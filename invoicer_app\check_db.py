#!/usr/bin/env python3
"""
Check database state for Word/Excel document support.
"""

import sys
import os
sys.path.insert(0, 'ui')

def check_database():
    """Check the current database state."""
    try:
        import sqlite3

        if os.path.exists('ui/data/invoices.db'):
            print('Database file exists')
            # Connect directly to the database
            conn = sqlite3.connect('ui/data/invoices.db')
            
            # Check what tables exist
            tables = conn.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall()
            print('Tables:', [table[0] for table in tables])
            
            # Check if invoice_imports table exists and has data
            if any(table[0] == 'invoice_imports' for table in tables):
                count = conn.execute("SELECT COUNT(*) FROM invoice_imports").fetchone()[0]
                print(f'invoice_imports table has {count} records')
                
                if count > 0:
                    # Check recent records
                    invoices = conn.execute("""
                        SELECT id, filename, 
                               CASE WHEN pdf_blob IS NOT NULL THEN 'YES' ELSE 'NO' END as has_pdf_blob,
                               CASE WHEN pdf_blob IS NOT NULL THEN LENGTH(pdf_blob) ELSE 0 END as blob_size
                        FROM invoice_imports 
                        ORDER BY id DESC LIMIT 5
                    """).fetchall()
                    
                    print('\nRecent invoices:')
                    for invoice in invoices:
                        print(f'  ID: {invoice[0]}, File: {invoice[1]}, Has PDF: {invoice[2]}, Size: {invoice[3]} bytes')
            else:
                print('invoice_imports table does not exist')
            
            conn.close()
        else:
            print('Database file does not exist')
            
    except Exception as e:
        print(f'Error checking database: {e}')

if __name__ == '__main__':
    check_database()
