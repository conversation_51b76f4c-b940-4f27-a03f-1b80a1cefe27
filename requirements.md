# MacInvoicer Requirements

## Core Dependencies
- openai>=1.82.0  # OpenAI API client
- python-dotenv>=1.1.0  # Environment variable management
- pandas>=2.2.0  # Data manipulation and analysis
- openpyxl>=3.1.0  # Excel file handling
- PyPDF2>=3.0.0  # PDF text extraction
- pdf2image>=1.17.0  # PDF to image conversion
- pytesseract>=0.3.13  # OCR for image text extraction
- Pillow>=11.0.0  # Image processing
- numpy>=2.2.0  # Numerical computations
- watchdog>=6.0.0  # File system monitoring

## UI Dependencies
- flask>=3.0.0  # Web framework for review UI
- flask-cors>=4.0.0  # Cross-origin resource sharing if needed
- werkzeug>=3.0.0  # WSGI utilities for Flask (file upload security)

## Development Dependencies
- pytest>=8.0.0  # Testing framework
- pytest-mock>=3.12.0  # Mock objects for testing

## Installation Commands
```bash
# Install core dependencies
pip install openai python-dotenv pandas openpyxl PyPDF2 pdf2image pytesseract Pillow numpy watchdog

# Install UI dependencies  
pip install flask flask-cors werkzeug

# Install development dependencies (optional)
pip install pytest pytest-mock
```

## PDF.js Setup
Download PDF.js from: https://github.com/mozilla/pdf.js/releases
Extract `pdf.min.js` to `ui/static/pdf.js/` directory 