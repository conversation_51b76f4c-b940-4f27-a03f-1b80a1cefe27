#!/usr/bin/env python3
"""
Test script to verify and fix PDF rotation direction.
"""

import sys
import os
import tempfile
import shutil

# Add src directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from pdf2image import convert_from_path
from src.pdf_orientation import detect_text_orientation_osd
from src.config_loader import POPPLER_PATH
import PyPDF2

def test_rotation_directions(pdf_path):
    """Test different rotation directions to find the correct one."""
    
    print(f"Testing rotation directions for: {os.path.basename(pdf_path)}")
    print("=" * 60)
    
    # First, check what OSD says about the original
    print("1. Original PDF OSD:")
    images = convert_from_path(pdf_path, first_page=1, last_page=1, poppler_path=POPPLER_PATH, dpi=200)
    if images:
        osd_result = detect_text_orientation_osd(images[0])
        print(f"   Orientation: {osd_result['orientation']}°")
        print(f"   Rotate needed: {osd_result['rotate']}°")
        print(f"   Script: {osd_result['script']}")
        print(f"   Confidence: {osd_result['confidence']:.1f}")
        
        rotate_needed = osd_result['rotate']
        print(f"\n   OSD says we need to rotate by: {rotate_needed}°")
    else:
        print("   Could not convert PDF to image")
        return
    
    # Test both directions of the needed rotation
    test_rotations = [rotate_needed, -rotate_needed, 360 - rotate_needed]
    
    for i, test_rotation in enumerate(test_rotations):
        print(f"\n2.{i+1} Testing rotation by {test_rotation}°:")
        
        # Create rotated PDF
        temp_fd, temp_path = tempfile.mkstemp(suffix='.pdf', prefix=f'test_rot_{test_rotation}_')
        os.close(temp_fd)
        
        try:
            # Apply rotation
            with open(pdf_path, 'rb') as input_file:
                pdf_reader = PyPDF2.PdfReader(input_file)
                pdf_writer = PyPDF2.PdfWriter()
                
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    page.rotate(test_rotation)
                    pdf_writer.add_page(page)
                
                with open(temp_path, 'wb') as output_file:
                    pdf_writer.write(output_file)
            
            # Test OSD on rotated PDF
            rotated_images = convert_from_path(temp_path, first_page=1, last_page=1, poppler_path=POPPLER_PATH, dpi=200)
            if rotated_images:
                osd_result = detect_text_orientation_osd(rotated_images[0])
                print(f"   After {test_rotation}° rotation:")
                print(f"   Orientation: {osd_result['orientation']}°")
                print(f"   Rotate needed: {osd_result['rotate']}°")
                print(f"   Script: {osd_result['script']}")
                print(f"   Confidence: {osd_result['confidence']:.1f}")
                
                if osd_result['rotate'] == 0:
                    print(f"   ✅ SUCCESS! {test_rotation}° rotation makes PDF correctly oriented")
                    # Save this version
                    output_path = f"correctly_rotated_{test_rotation}_degrees.pdf"
                    shutil.copy2(temp_path, output_path)
                    print(f"   Saved as: {output_path}")
                else:
                    print(f"   ❌ Still needs {osd_result['rotate']}° more rotation")
            
        except Exception as e:
            print(f"   Error: {e}")
        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)

def main():
    """Main function."""
    if len(sys.argv) != 2:
        print("Usage: python test_rotation_fix.py <pdf_file_path>")
        return 1
    
    pdf_path = sys.argv[1]
    
    # Make path absolute if relative
    if not os.path.isabs(pdf_path):
        pdf_path = os.path.abspath(pdf_path)
    
    if not os.path.exists(pdf_path):
        print(f"Error: PDF file not found: {pdf_path}")
        return 1
    
    test_rotation_directions(pdf_path)
    
    print("\n" + "=" * 60)
    print("Test completed! Check which rotation direction produces rotate=0°")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
