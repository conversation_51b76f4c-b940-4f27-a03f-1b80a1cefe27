# MacInvoicer Review UI Development Guide

## Core Objective
Build a minimal web interface to review AI-extracted invoice data. Users see the PDF and extracted data side-by-side, then approve, edit, or reject.

## Technology Stack
- **Backend**: Flask
- **Database**: SQLite
- **Frontend**: HTML + JavaScript (no frameworks)
- **PDF Viewer**: PDF.js

## Essential Features Only

### 1. Database Setup (SQLite)
```sql
-- Single table to track reviews
CREATE TABLE invoice_reviews (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    filename TEXT NOT NULL,
    pdf_path TEXT NOT NULL,
    extracted_data TEXT NOT NULL,  -- JSON string
    reviewed_data TEXT,            -- JSO<PERSON> string (null until edited)
    status TEXT DEFAULT 'pending', -- pending/approved/rejected
    review_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_at TIMESTAMP
);
```

### 2. Flask App Structure
```
ui/
├── app.py              # Main Flask app
├── import_data.py      # One-time data import script
├── templates/
│   ├── index.html      # Invoice list
│   └── review.html     # Review interface
├── static/
│   ├── style.css       # Basic styling
│   ├── app.js          # JavaScript functionality
│   └── pdf.js/         # PDF.js library (download separately)
└── data/
    └── invoices.db     # SQLite database
```

### 3. Core Routes
```python
@app.route('/')                          # List all invoices
@app.route('/review/<int:invoice_id>')   # Review interface
@app.route('/api/invoice/<int:id>')      # Get invoice data (JSON)
@app.route('/api/approve/<int:id>')      # Approve invoice (POST)
@app.route('/api/reject/<int:id>')       # Reject invoice (POST)
@app.route('/api/update/<int:id>')       # Update invoice data (POST)
@app.route('/pdf/<int:id>')              # Serve PDF file
@app.route('/export/approved')           # Export approved to Excel
@app.route('/export/rejected')           # Export rejected to Excel
```

### 4. Review Interface Layout
**Simple split-screen HTML:**
- Left side (60%): PDF viewer using PDF.js
- Right side (40%): Form with extracted data
- Bottom: 3 buttons (Approve, Reject, Save Changes)

### 5. Data Integration
**One-time data import script (`import_data.py`):**
```python
def import_existing_data():
    # Read Excel files from ../output_spreadsheets/
    # Read PDF files from ../monitored_invoices/
    # Insert into SQLite database
    pass
```

### 6. Minimal Workflow
1. **List View**: Show all invoices with status and basic info
2. **Review**: Display PDF + editable form side-by-side
3. **Actions**: 
   - Approve → status = 'approved' 
   - Reject → status = 'rejected' + notes
   - Edit → update reviewed_data + status = 'approved'

## Implementation Files

### 7. Flask App (`app.py`)
```python
from flask import Flask, render_template, request, jsonify, send_file
import sqlite3
import json
import os
from datetime import datetime

app = Flask(__name__)
DATABASE = 'data/invoices.db'

def get_db():
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

@app.route('/')
def index():
    conn = get_db()
    invoices = conn.execute(
        'SELECT * FROM invoice_reviews ORDER BY created_at DESC'
    ).fetchall()
    conn.close()
    return render_template('index.html', invoices=invoices)

@app.route('/review/<int:invoice_id>')
def review(invoice_id):
    conn = get_db()
    invoice = conn.execute(
        'SELECT * FROM invoice_reviews WHERE id = ?', (invoice_id,)
    ).fetchone()
    conn.close()
    if not invoice:
        return "Invoice not found", 404
    return render_template('review.html', invoice=invoice)

@app.route('/api/invoice/<int:invoice_id>')
def get_invoice_data(invoice_id):
    conn = get_db()
    invoice = conn.execute(
        'SELECT * FROM invoice_reviews WHERE id = ?', (invoice_id,)
    ).fetchone()
    conn.close()
    if not invoice:
        return jsonify({'error': 'Invoice not found'}), 404
    
    data = json.loads(invoice['reviewed_data'] or invoice['extracted_data'])
    return jsonify(data)

@app.route('/api/approve/<int:invoice_id>', methods=['POST'])
def approve_invoice(invoice_id):
    conn = get_db()
    conn.execute(
        'UPDATE invoice_reviews SET status = ?, reviewed_at = ? WHERE id = ?',
        ('approved', datetime.now(), invoice_id)
    )
    conn.commit()
    conn.close()
    return jsonify({'success': True})

@app.route('/api/reject/<int:invoice_id>', methods=['POST'])
def reject_invoice(invoice_id):
    notes = request.json.get('notes', '')
    conn = get_db()
    conn.execute(
        'UPDATE invoice_reviews SET status = ?, review_notes = ?, reviewed_at = ? WHERE id = ?',
        ('rejected', notes, datetime.now(), invoice_id)
    )
    conn.commit()
    conn.close()
    return jsonify({'success': True})

@app.route('/api/update/<int:invoice_id>', methods=['POST'])
def update_invoice(invoice_id):
    updated_data = request.json
    conn = get_db()
    conn.execute(
        'UPDATE invoice_reviews SET reviewed_data = ?, status = ?, reviewed_at = ? WHERE id = ?',
        (json.dumps(updated_data), 'approved', datetime.now(), invoice_id)
    )
    conn.commit()
    conn.close()
    return jsonify({'success': True})

@app.route('/pdf/<int:invoice_id>')
def serve_pdf(invoice_id):
    conn = get_db()
    invoice = conn.execute(
        'SELECT pdf_path FROM invoice_reviews WHERE id = ?', (invoice_id,)
    ).fetchone()
    conn.close()
    if not invoice or not os.path.exists(invoice['pdf_path']):
        return "PDF not found", 404
    return send_file(invoice['pdf_path'])

if __name__ == '__main__':
    # Create database if it doesn't exist
    os.makedirs('data', exist_ok=True)
    conn = sqlite3.connect(DATABASE)
    conn.execute('''
        CREATE TABLE IF NOT EXISTS invoice_reviews (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            filename TEXT NOT NULL,
            pdf_path TEXT NOT NULL,
            extracted_data TEXT NOT NULL,
            reviewed_data TEXT,
            status TEXT DEFAULT 'pending',
            review_notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            reviewed_at TIMESTAMP
        )
    ''')
    conn.close()
    
    app.run(debug=True, port=5000)
```

### 8. Invoice List Template (`templates/index.html`)
```html
<!DOCTYPE html>
<html>
<head>
    <title>Invoice Review Dashboard</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <h1>Invoice Review Dashboard</h1>
        
        <div class="stats">
            <div class="stat-card">
                <h3>Pending</h3>
                <p>{{ invoices|selectattr('status', 'equalto', 'pending')|list|length }}</p>
            </div>
            <div class="stat-card">
                <h3>Approved</h3>
                <p>{{ invoices|selectattr('status', 'equalto', 'approved')|list|length }}</p>
            </div>
            <div class="stat-card">
                <h3>Rejected</h3>
                <p>{{ invoices|selectattr('status', 'equalto', 'rejected')|list|length }}</p>
            </div>
        </div>

        <table class="invoice-table">
            <thead>
                <tr>
                    <th>Filename</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                {% for invoice in invoices %}
                <tr class="status-{{ invoice.status }}">
                    <td>{{ invoice.filename }}</td>
                    <td>{{ invoice.status.title() }}</td>
                    <td>{{ invoice.created_at[:16] }}</td>
                    <td>
                        <a href="{{ url_for('review', invoice_id=invoice.id) }}" class="btn">Review</a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <div class="export-section">
            <h3>Export Data</h3>
            <a href="/export/approved" class="btn btn-success">Export Approved</a>
            <a href="/export/rejected" class="btn btn-danger">Export Rejected</a>
        </div>
    </div>
</body>
</html>
```

### 9. Review Interface Template (`templates/review.html`)
```html
<!DOCTYPE html>
<html>
<head>
    <title>Review: {{ invoice.filename }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <script src="{{ url_for('static', filename='pdf.js/pdf.min.js') }}"></script>
</head>
<body>
    <div class="review-container">
        <div class="header">
            <h1>Review: {{ invoice.filename }}</h1>
            <a href="{{ url_for('index') }}" class="btn">← Back to List</a>
        </div>

        <div class="review-content">
            <!-- PDF Viewer -->
            <div class="pdf-section">
                <div class="pdf-controls">
                    <button onclick="prevPage()">Previous</button>
                    <span id="page-info">Page 1 of 1</span>
                    <button onclick="nextPage()">Next</button>
                    <button onclick="zoomIn()">Zoom In</button>
                    <button onclick="zoomOut()">Zoom Out</button>
                </div>
                <canvas id="pdf-canvas"></canvas>
            </div>

            <!-- Data Form -->
            <div class="form-section">
                <form id="invoice-form">
                    <div class="field-group">
                        <label for="invoice_id">Invoice ID:</label>
                        <input type="text" id="invoice_id" name="invoice_id">
                    </div>

                    <div class="field-group">
                        <label for="vendor_name">Vendor Name:</label>
                        <input type="text" id="vendor_name" name="vendor_name">
                    </div>

                    <div class="field-group">
                        <label for="invoice_date">Invoice Date:</label>
                        <input type="date" id="invoice_date" name="invoice_date">
                    </div>

                    <div class="field-group">
                        <label for="due_date">Due Date:</label>
                        <input type="date" id="due_date" name="due_date">
                    </div>

                    <div class="field-group">
                        <label for="total_amount_eur">Total Amount (EUR):</label>
                        <input type="number" step="0.01" id="total_amount_eur" name="total_amount_eur">
                    </div>

                    <div class="field-group">
                        <label for="vat_amount_eur">VAT Amount (EUR):</label>
                        <input type="number" step="0.01" id="vat_amount_eur" name="vat_amount_eur">
                    </div>

                    <div class="field-group">
                        <label for="business_unit">Business Unit:</label>
                        <input type="text" id="business_unit" name="business_unit">
                    </div>

                    <div class="field-group">
                        <label for="review_notes">Review Notes:</label>
                        <textarea id="review_notes" name="review_notes" rows="3"></textarea>
                    </div>
                </form>

                <div class="action-buttons">
                    <button onclick="saveChanges()" class="btn btn-primary">Save Changes</button>
                    <button onclick="approveInvoice()" class="btn btn-success">Approve</button>
                    <button onclick="showRejectModal()" class="btn btn-danger">Reject</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Reject Modal -->
    <div id="reject-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3>Reject Invoice</h3>
            <textarea id="reject-notes" placeholder="Reason for rejection..." rows="4"></textarea>
            <div class="modal-buttons">
                <button onclick="confirmReject()" class="btn btn-danger">Confirm Reject</button>
                <button onclick="closeRejectModal()" class="btn">Cancel</button>
            </div>
        </div>
    </div>

    <script>
        const invoiceId = {{ invoice.id }};
        loadInvoiceData();
        loadPDF();
    </script>
    <script src="{{ url_for('static', filename='app.js') }}"></script>
</body>
</html>
```

### 10. JavaScript Functions (`static/app.js`)
```javascript
let pdfDoc = null;
let pageNum = 1;
let pageRendering = false;
let pageNumPending = null;
let scale = 1.2;
const canvas = document.getElementById('pdf-canvas');
const ctx = canvas.getContext('2d');

// PDF Functions
function loadPDF() {
    const url = `/pdf/${invoiceId}`;
    pdfjsLib.getDocument(url).promise.then(function(pdfDoc_) {
        pdfDoc = pdfDoc_;
        document.getElementById('page-info').textContent = `Page ${pageNum} of ${pdfDoc.numPages}`;
        renderPage(pageNum);
    });
}

function renderPage(num) {
    pageRendering = true;
    pdfDoc.getPage(num).then(function(page) {
        const viewport = page.getViewport({scale: scale});
        canvas.height = viewport.height;
        canvas.width = viewport.width;

        const renderContext = {
            canvasContext: ctx,
            viewport: viewport
        };
        const renderTask = page.render(renderContext);

        renderTask.promise.then(function() {
            pageRendering = false;
            if (pageNumPending !== null) {
                renderPage(pageNumPending);
                pageNumPending = null;
            }
        });
    });

    document.getElementById('page-info').textContent = `Page ${num} of ${pdfDoc.numPages}`;
}

function queueRenderPage(num) {
    if (pageRendering) {
        pageNumPending = num;
    } else {
        renderPage(num);
    }
}

function prevPage() {
    if (pageNum <= 1) return;
    pageNum--;
    queueRenderPage(pageNum);
}

function nextPage() {
    if (pageNum >= pdfDoc.numPages) return;
    pageNum++;
    queueRenderPage(pageNum);
}

function zoomIn() {
    scale += 0.2;
    queueRenderPage(pageNum);
}

function zoomOut() {
    if (scale <= 0.4) return;
    scale -= 0.2;
    queueRenderPage(pageNum);
}

// Data Functions
function loadInvoiceData() {
    fetch(`/api/invoice/${invoiceId}`)
        .then(response => response.json())
        .then(data => {
            // Populate form fields
            Object.keys(data).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.value = data[key] || '';
                }
            });
        })
        .catch(error => console.error('Error loading invoice data:', error));
}

function getFormData() {
    const form = document.getElementById('invoice-form');
    const formData = new FormData(form);
    const data = {};
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    return data;
}

function saveChanges() {
    const data = getFormData();
    fetch(`/api/update/${invoiceId}`, {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert('Changes saved and invoice approved!');
            window.location.href = '/';
        }
    })
    .catch(error => console.error('Error saving changes:', error));
}

function approveInvoice() {
    fetch(`/api/approve/${invoiceId}`, {
        method: 'POST',
        headers: {'Content-Type': 'application/json'}
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert('Invoice approved!');
            window.location.href = '/';
        }
    })
    .catch(error => console.error('Error approving invoice:', error));
}

function showRejectModal() {
    document.getElementById('reject-modal').style.display = 'block';
}

function closeRejectModal() {
    document.getElementById('reject-modal').style.display = 'none';
}

function confirmReject() {
    const notes = document.getElementById('reject-notes').value;
    fetch(`/api/reject/${invoiceId}`, {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({notes: notes})
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert('Invoice rejected!');
            window.location.href = '/';
        }
    })
    .catch(error => console.error('Error rejecting invoice:', error));
}
```

### 11. Basic Styling (`static/style.css`)
```css
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.stats {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
    flex: 1;
}

.invoice-table {
    width: 100%;
    background: white;
    border-collapse: collapse;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.invoice-table th,
.invoice-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.invoice-table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

.status-pending { background-color: #fff3cd; }
.status-approved { background-color: #d4edda; }
.status-rejected { background-color: #f8d7da; }

.btn {
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    display: inline-block;
    background-color: #007bff;
    color: white;
}

.btn:hover { background-color: #0056b3; }
.btn-success { background-color: #28a745; }
.btn-success:hover { background-color: #1e7e34; }
.btn-danger { background-color: #dc3545; }
.btn-danger:hover { background-color: #bd2130; }
.btn-primary { background-color: #007bff; }

.review-container {
    max-width: 1600px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.review-content {
    display: flex;
    gap: 20px;
    height: 80vh;
}

.pdf-section {
    flex: 0 0 60%;
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pdf-controls {
    margin-bottom: 10px;
    display: flex;
    gap: 10px;
    align-items: center;
}

.pdf-controls button {
    padding: 5px 10px;
    border: 1px solid #ccc;
    background: white;
    border-radius: 4px;
    cursor: pointer;
}

#pdf-canvas {
    max-width: 100%;
    border: 1px solid #ddd;
}

.form-section {
    flex: 1;
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow-y: auto;
}

.field-group {
    margin-bottom: 15px;
}

.field-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.field-group input,
.field-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}

.action-buttons {
    margin-top: 20px;
    display: flex;
    gap: 10px;
}

.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.4);
}

.modal-content {
    background-color: white;
    margin: 15% auto;
    padding: 20px;
    border-radius: 8px;
    width: 500px;
    max-width: 90%;
}

.modal-buttons {
    margin-top: 15px;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.export-section {
    margin-top: 30px;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
```

### 12. Data Import Script (`import_data.py`)
```python
import sqlite3
import pandas as pd
import os
import json
import glob
from datetime import datetime

DATABASE = 'data/invoices.db'
EXCEL_PATH = '../output_spreadsheets/'
PDF_PATH = '../monitored_invoices/'

def import_existing_data():
    """Import data from existing Excel files and link to PDFs"""
    
    # Connect to database
    conn = sqlite3.connect(DATABASE)
    
    # Get all Excel files
    excel_files = glob.glob(os.path.join(EXCEL_PATH, '*_invoices.xlsx'))
    
    for excel_file in excel_files:
        print(f"Processing {excel_file}")
        
        try:
            df = pd.read_excel(excel_file)
            
            for _, row in df.iterrows():
                filename = row.get('Invoice_Filename', '')
                if not filename:
                    continue
                
                # Find corresponding PDF
                pdf_path = os.path.join(PDF_PATH, filename)
                if not os.path.exists(pdf_path):
                    print(f"PDF not found: {pdf_path}")
                    continue
                
                # Extract data from row
                extracted_data = {
                    'invoice_id': row.get('Invoice_ID'),
                    'vendor_name': row.get('Vendor_Name'),
                    'invoice_date': row.get('Invoice_Date'),
                    'due_date': row.get('Due_Date'),
                    'total_amount_eur': row.get('Total_Amount_EUR'),
                    'vat_amount_eur': row.get('VAT_Amount_EUR'),
                    'business_unit': row.get('Business_Unit'),
                    'original_currency': row.get('Original_Currency'),
                    'original_total_amount': row.get('Original_Total_Amount'),
                    'ai_confidence_score': row.get('AI_Confidence_Score', 0.5)
                }
                
                # Check if already exists
                existing = conn.execute(
                    'SELECT id FROM invoice_reviews WHERE filename = ?', 
                    (filename,)
                ).fetchone()
                
                if not existing:
                    conn.execute('''
                        INSERT INTO invoice_reviews 
                        (filename, pdf_path, extracted_data, status, created_at)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (
                        filename,
                        pdf_path,
                        json.dumps(extracted_data),
                        'pending',
                        datetime.now()
                    ))
                    print(f"Imported: {filename}")
                else:
                    print(f"Already exists: {filename}")
        
        except Exception as e:
            print(f"Error processing {excel_file}: {e}")
    
    conn.commit()
    conn.close()
    print("Import completed!")

if __name__ == '__main__':
    import_existing_data()
```

## Implementation Steps

### Step 1: Setup Environment
```bash
cd macinvoicer
mkdir ui
cd ui
pip install flask pandas openpyxl
```

### Step 2: Download PDF.js
```bash
mkdir -p static/pdf.js
# Download PDF.js from https://github.com/mozilla/pdf.js/releases
# Extract pdf.min.js to static/pdf.js/
```

### Step 3: Create Files
1. Create all files as shown above
2. Run `python import_data.py` to import existing data
3. Run `python app.py` to start the server
4. Visit `http://localhost:5000`

### Step 4: Test Workflow
1. ✅ See list of invoices
2. ✅ Click "Review" on any invoice
3. ✅ View PDF and edit form data
4. ✅ Test Approve/Reject buttons
5. ✅ Verify data is saved

## Success Criteria
- ✅ Can view list of processed invoices  
- ✅ Can open PDF and see extracted data side-by-side  
- ✅ Can edit any field in the form  
- ✅ Can approve invoice (saves to approved list)  
- ✅ Can reject invoice (saves to rejected list)  
- ✅ Basic export functionality works

**That's it. No fancy features until this works.**