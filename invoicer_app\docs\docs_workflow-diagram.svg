<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="580" height="330" viewBox="0 0 580 330" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Nodes -->
  <rect x="40" y="20" width="120" height="40" rx="8" fill="#E0F7FA" stroke="#0097A7" stroke-width="2"/>
  <text x="100" y="45" text-anchor="middle" font-family="Verdana" font-size="15" fill="#006064">PDF Invoice Added</text>

  <rect x="220" y="20" width="170" height="40" rx="8" fill="#E3F2FD" stroke="#1976D2" stroke-width="2"/>
  <text x="305" y="45" text-anchor="middle" font-family="Verdana" font-size="15" fill="#0D47A1">Directory Monitor Detects File</text>

  <rect x="440" y="20" width="110" height="40" rx="8" fill="#FFF3E0" stroke="#FF9800" stroke-width="2"/>
  <text x="495" y="45" text-anchor="middle" font-family="Verdana" font-size="13" fill="#E65100">Text Extraction</text>
  <text x="495" y="60" text-anchor="middle" font-family="Verdana" font-size="11" fill="#E65100">(PyPDF2/OCR)</text>

  <rect x="80" y="110" width="170" height="40" rx="8" fill="#F3E5F5" stroke="#7B1FA2" stroke-width="2"/>
  <text x="165" y="135" text-anchor="middle" font-family="Verdana" font-size="15" fill="#4A148C">AI Data Extraction</text>
  <text x="165" y="150" text-anchor="middle" font-family="Verdana" font-size="11" fill="#4A148C">(OpenAI)</text>

  <rect x="320" y="110" width="170" height="40" rx="8" fill="#F1F8E9" stroke="#388E3C" stroke-width="2"/>
  <text x="405" y="135" text-anchor="middle" font-family="Verdana" font-size="15" fill="#1B5E20">Append to Monthly</text>
  <text x="405" y="150" text-anchor="middle" font-family="Verdana" font-size="13" fill="#1B5E20">Spreadsheet</text>

  <rect x="80" y="200" width="170" height="40" rx="8" fill="#FFFDE7" stroke="#FBC02D" stroke-width="2"/>
  <text x="165" y="225" text-anchor="middle" font-family="Verdana" font-size="15" fill="#F57F17">Create Processing Note</text>

  <rect x="320" y="200" width="170" height="40" rx="8" fill="#ECEFF1" stroke="#455A64" stroke-width="2"/>
  <text x="405" y="225" text-anchor="middle" font-family="Verdana" font-size="15" fill="#263238">Log Processing &amp; Costs</text>

  <!-- Arrows -->
  <line x1="160" y1="40" x2="220" y2="40" stroke="#444" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="390" y1="40" x2="440" y2="40" stroke="#444" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="495" y1="60" x2="165" y2="110" stroke="#444" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="250" y1="130" x2="320" y2="130" stroke="#444" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="405" y1="150" x2="165" y2="200" stroke="#444" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="250" y1="220" x2="320" y2="220" stroke="#444" stroke-width="2" marker-end="url(#arrow)"/>

  <!-- Arrowhead marker definition -->
  <defs>
    <marker id="arrow" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto" markerUnits="strokeWidth">
      <polygon points="0 0, 10 3.5, 0 7" fill="#444"/>
    </marker>
  </defs>
</svg>