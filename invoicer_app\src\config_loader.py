"""
Configuration loader module for the MacInvoicer application.

This module handles all configuration loading and environment setup for the application.
It loads environment variables from a .env file in the config directory and sets up
all necessary paths and configurations needed by other modules.

Key Features:
    - Environment variable loading from .env file
    - Directory path management and creation
    - API key and model configuration
    - Tool path configuration (<PERSON><PERSON>ct, Poppler)
    - Automatic directory structure setup

Author: MacInvoicer Development Team
Version: 1.0.0
"""

import os
from dotenv import load_dotenv

# Determine project base path (macinvoicer directory)
# Assumes config_loader.py is in macinvoicer/src/
PROJECT_BASE_PATH = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Path to the .env file inside the config directory
CONFIG_DIR = os.path.join(PROJECT_BASE_PATH, 'config')
DOTENV_PATH = os.path.join(CONFIG_DIR, '.env')

# Load environment variables from .env file in config directory
if os.path.exists(DOTENV_PATH):
    load_dotenv(dotenv_path=DOTENV_PATH)
    # print(f"Loaded .env file from {DOTENV_PATH}")
else:
    print(f"Warning: .env file not found at {DOTENV_PATH}. Key functionalities might not work.")

OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-4.1-nano") # Default to gpt-4.1-nano if not set
TESSERACT_CMD_PATH = os.getenv("TESSERACT_CMD_PATH") # Optional: e.g., C:\Program Files\Tesseract-OCR\tesseract.exe
TESSERACT_PATH = TESSERACT_CMD_PATH  # Alias for consistency with pdf_orientation module
POPPLER_PATH = os.getenv("POPPLER_PATH") # Ensure POPPLER_PATH is loaded

# Hybrid processing configuration
USE_HYBRID_EXTRACTION = os.getenv("USE_HYBRID_EXTRACTION", "false").lower() in ["true", "1", "yes", "on"]

# VAT Rate Configuration
VAT_RATES_STR = os.getenv("VAT_RATES", "23,13.5,9,0")
VAT_RATES = [float(rate.strip()) for rate in VAT_RATES_STR.split(",") if rate.strip()]
DEFAULT_VAT_RATE = float(os.getenv("DEFAULT_VAT_RATE", "23"))

MONITORED_DIR_NAME = "monitored_invoices"
OUTPUT_DIR_NAME = "output_spreadsheets"
PROCESSED_NOTES_DIR_NAME = "processed_invoices_notes"
LOGS_DIR_NAME = "logs"
CONFIG_DIR_NAME = "config" # Added for ensure_directories_exist consistency

MONITORED_PATH = os.path.join(PROJECT_BASE_PATH, MONITORED_DIR_NAME)
OUTPUT_PATH = os.path.join(PROJECT_BASE_PATH, OUTPUT_DIR_NAME)
PROCESSED_NOTES_PATH = os.path.join(PROJECT_BASE_PATH, PROCESSED_NOTES_DIR_NAME)
LOGS_PATH = os.path.join(PROJECT_BASE_PATH, LOGS_DIR_NAME)

# Ensure directories exist when this module is loaded
def ensure_directories_exist():
    """
    Create all required directories if they don't exist.
    
    This function ensures that all necessary directories for the application
    are created during module initialization. It creates directories for
    monitored invoices, output spreadsheets, processing notes, logs, and config.
    
    Returns:
        None: Function performs directory creation side effects.
        
    Note:
        This function is called automatically when the module is imported.
        Directory creation is logged to stdout since the logger may not be
        initialized yet.
    """
    for path in [MONITORED_PATH, OUTPUT_PATH, PROCESSED_NOTES_PATH, LOGS_PATH, CONFIG_DIR]:
        if not os.path.exists(path):
            print(f"Creating directory: {path}")
            os.makedirs(path)

ensure_directories_exist()

# Initial setup warnings (before logger might be fully available)
if not OPENAI_API_KEY:
    print("Warning: OPENAI_API_KEY is not set. OpenAI features will be disabled.")
if TESSERACT_CMD_PATH:
    print(f"Using Tesseract CMD from: {TESSERACT_CMD_PATH}")
else:
    print("TESSERACT_CMD_PATH not set in .env, assuming tesseract is in system PATH for OCR.")
if POPPLER_PATH:
    print(f"Using Poppler from: {POPPLER_PATH}")
else:
    print("POPPLER_PATH not set in .env. PDF to image conversion for OCR might fail if Poppler is not in system PATH.")

# VAT configuration info
print(f"Configured VAT rates: {VAT_RATES}%")
print(f"Default VAT rate: {DEFAULT_VAT_RATE}%")