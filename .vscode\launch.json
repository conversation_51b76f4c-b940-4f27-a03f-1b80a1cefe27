{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Flask App",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/invoicer_app/ui/app.py",
            "console": "integratedTerminal",
            "jinja": true,
            "env": {
                "FLASK_DEBUG": "1" 
            },
            "cwd": "${workspaceFolder}"
        },
        {
            "name": "Chrome: Launch against Flask",
            "type": "chrome",
            "request": "launch",
            "url": "http://localhost:5000",
            "webRoot": "${workspaceFolder}/invoicer_app/ui"
        }
    ],
    "compounds": [
        {
            "name": "Debug: Flask (Python) + Chrome (JS)",
            "configurations": ["Python: Flask App", "Chrome: Launch against Flask"],
            "stopAll": true
        }
    ]
}